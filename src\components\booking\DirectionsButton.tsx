import React from 'react';
import { Navigation } from 'lucide-react';
import {
  useGeolocation,
  extractBookingCoordinates,
} from '@/hooks/useGeolocation';
import { Booking } from '@/store/api/customerApiSlice';

interface DirectionsButtonProps {
  booking: Booking;
  className?: string;
  title?: string;
  children?: React.ReactNode;
}

const DirectionsButton: React.FC<DirectionsButtonProps> = ({
  booking,
  className = 'flex items-center gap-1 px-3 py-2 text-[#7039D6] bg-[#7039D62E] rounded-lg hover:bg-opacity-85 transition-colors text-sm font-medium',
  title = 'Get directions from your current location to customer location',
  children,
}) => {
  const { getDirections } = useGeolocation();

  const handleGetDirections = (e: React.MouseEvent) => {
    e.stopPropagation();

    const coordinates = extractBookingCoordinates(booking);
    if (coordinates) {
      getDirections(coordinates.lat, coordinates.lng);
    }
  };

  return (
    <button onClick={handleGetDirections} className={className} title={title}>
      <Navigation className='w-4 h-4' />
      {children || 'Get Directions'}
    </button>
  );
};

export default DirectionsButton;
