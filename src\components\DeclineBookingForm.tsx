import React, { useRef, useEffect } from 'react';
import { X } from 'lucide-react';

interface DeclineBookingFormProps {
  isOpen: boolean;
  declineReason: string;
  setDeclineReason: (reason: string) => void;
  declineReasonError: string;
  setDeclineReasonError: (error: string) => void;
  isSubmittingDecline: boolean;
  onSubmit: () => void;
  onCancel: () => void;
}

const DeclineBookingForm: React.FC<DeclineBookingFormProps> = ({
  isOpen,
  declineReason,
  setDeclineReason,
  declineReasonError,
  setDeclineReasonError,
  isSubmittingDecline,
  onSubmit,
  onCancel,
}) => {
  const declineTextareaRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    if (isOpen && declineTextareaRef.current) {
      setTimeout(() => {
        declineTextareaRef.current?.focus();
        declineTextareaRef.current?.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
        });
      }, 100);
    }
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className='mt-4 p-4 bg-white rounded-lg border border-gray-200 transition-all duration-400 ease-out'>
      <div className='flex justify-between items-center mb-3'>
        <h3 className='text-lg font-semibold text-gray-900'>Decline Booking</h3>
        <button
          onClick={onCancel}
          className='p-1 rounded-full hover:bg-gray-100 transition-colors'
          disabled={isSubmittingDecline}
        >
          <X className='w-5 h-5 text-gray-500 hover:text-red-500' />
        </button>
      </div>

      <div className='space-y-3'>
        <div>
          <label className='block text-sm font-medium text-gray-700 mb-2'>
            Please share the reason for declining the booking to help inform the
            customer:{' '}
          </label>
          <div className='flex md:flex-row flex-col gap-3'>
            <div className='flex-1'>
              <textarea
                ref={declineTextareaRef}
                value={declineReason}
                onChange={e => {
                  setDeclineReason(e.target.value);
                  if (declineReasonError) {
                    setDeclineReasonError('');
                  }
                }}
                className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-nursery-blue focus:border-transparent resize-none'
                rows={3}
                disabled={isSubmittingDecline}
              />
              {declineReasonError && (
                <p className='text-red-500 text-sm mt-1'>
                  {declineReasonError}
                </p>
              )}
              <p className='text-gray-500 text-sm mt-1'>
                {declineReason.length}/50 characters minimum
              </p>
            </div>
            <div className='md:flex md:items-center items-center md:justify-center flex justify-center'>
              <button
                onClick={onSubmit}
                disabled={
                  isSubmittingDecline || declineReason.trim().length < 50
                }
                className='px-6 py-2 bg-[#EB001B] text-white rounded-lg hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center gap-2 h-fit min-w-[120px]'
              >
                {isSubmittingDecline ? (
                  <>
                    <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-white'></div>
                    <span>Declining...</span>
                  </>
                ) : (
                  <span>Decline</span>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DeclineBookingForm;
