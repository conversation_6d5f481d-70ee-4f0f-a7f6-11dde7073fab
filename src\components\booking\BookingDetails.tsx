import React from 'react';
import {
  formatTime12Hour,
  formatDateShort,
  formatServicesSelected,
} from '@/utils/dateTime';
import { Booking } from '@/store/api/customerApiSlice';

interface BookingDetailsProps {
  booking: Booking;
}

const BookingDetails: React.FC<BookingDetailsProps> = ({ booking }) => {
  return (
    <div className='flex md:flex-row flex-col md:justify-start md:gap-8 gap-1'>
      <div>
        <div className='text-sm text-gray-800'>Booking ID</div>
        <div className='font-semibold text-gray-900'>#{booking.booking_id}</div>
      </div>
      <div className='flex items-center gap-6 md:gap-7'>
        <div>
          <div className='text-sm text-gray-800'>Service Date</div>
          <div className='font-semibold text-gray-900'>
            {formatDateShort(booking.booked_date)}
          </div>
        </div>
        <div>
          <div className='text-sm text-gray-800'>Service Time</div>
          <div className='font-semibold text-gray-900'>
            {formatTime12Hour(booking.booked_slot)}
          </div>
        </div>
      </div>
      <div>
        <div className='text-sm text-gray-800 mb-1'>Service Type</div>
        <div className='font-semibold text-gray-900'>
          {formatServicesSelected(booking.services_selected)}
        </div>
      </div>
    </div>
  );
};

export default BookingDetails;
