import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { FilePlus2, X, Loader2, AlertCircle } from 'lucide-react';

interface DocumentUploadFormProps {
  formData: {
    idProofFiles: File[];
    experienceProofFiles: File[];
    otherDocumentFiles: File[];
    [key: string]: unknown;
  };
  updateFormData: (newData: Record<string, unknown>) => void;
  handleSubmit: () => void;
  handleBack: () => void;
  uploading?: boolean;
}

const DocumentUploadForm = ({
  formData,
  updateFormData,
  handleSubmit,
  handleBack,
  uploading = false,
}: DocumentUploadFormProps) => {
  const [validationErrors, setValidationErrors] = useState({
    idProofFiles: false,
    experienceProofFiles: false,
    otherDocumentFiles: false,
  });

  const [fileMetadata, setFileMetadata] = useState({
    idProofFilesMetadata: [],
    experienceProofFilesMetadata: [],
    otherDocumentFilesMetadata: [],
  });

  useEffect(() => {
    const savedData = localStorage.getItem('onboarding_form_data');
    if (savedData) {
      try {
        const parsedData = JSON.parse(savedData);

        const metadata = {
          idProofFilesMetadata: parsedData.idProofFilesMetadata
            ? JSON.parse(parsedData.idProofFilesMetadata)
            : [],
          experienceProofFilesMetadata: parsedData.experienceProofFilesMetadata
            ? JSON.parse(parsedData.experienceProofFilesMetadata)
            : [],
          otherDocumentFilesMetadata: parsedData.otherDocumentFilesMetadata
            ? JSON.parse(parsedData.otherDocumentFilesMetadata)
            : [],
        };

        setFileMetadata(metadata);
      } catch (error) {
        console.error('Failed to parse saved metadata:', error);
      }
    }
  }, []);

  const handleFileChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    fileType: 'idProofFiles' | 'experienceProofFiles' | 'otherDocumentFiles'
  ) => {
    if (event.target.files) {
      const filesList = Array.from(event.target.files);

      const metadataKey = `${fileType}Metadata`;
      const metadata = fileMetadata[metadataKey] || [];

      const updatedFiles = [...formData[fileType]];

      filesList.forEach(file => {
        const _matchingMetadata = metadata.find(
          meta => meta.name === file.name
        );

        const existingFileIndex = updatedFiles.findIndex(
          existing => existing.name === file.name
        );

        if (existingFileIndex >= 0) {
          updatedFiles[existingFileIndex] = file;
        } else {
          updatedFiles.push(file);
        }
      });

      updateFormData({
        [fileType]: updatedFiles,
      });

      if (validationErrors[fileType]) {
        setValidationErrors({
          ...validationErrors,
          [fileType]: false,
        });
      }
    }
  };

  const removeFile = (
    fileIndex: number,
    fileType: 'idProofFiles' | 'experienceProofFiles' | 'otherDocumentFiles'
  ) => {
    const updatedFiles = [...formData[fileType]];
    updatedFiles.splice(fileIndex, 1);
    updateFormData({ [fileType]: updatedFiles });
  };

  const validateForm = () => {
    const errors = {
      idProofFiles: formData.idProofFiles.length === 0,
      experienceProofFiles: formData.experienceProofFiles.length === 0,
      otherDocumentFiles: false,
    };

    setValidationErrors(errors);

    return !Object.values(errors).some(hasError => hasError);
  };

  const handleFormSubmit = () => {
    if (validateForm()) {
      handleSubmit();
    } else {
      for (const section of ['idProof', 'experienceProof', 'otherDocument']) {
        const errorSection = document.getElementById(`${section}-section`);
        if (errorSection) {
          errorSection.scrollIntoView({ behavior: 'smooth' });
          break;
        }
      }
    }
  };

  const renderFileList = (
    files: File[],
    fileType: 'idProofFiles' | 'experienceProofFiles' | 'otherDocumentFiles'
  ) => {
    const metadataKey = `${fileType}Metadata`;
    const metadata = fileMetadata[metadataKey] || [];

    if (files.length > 0) {
      return files.map((file, index) => {
        return (
          <div
            key={`file-${index}`}
            className='flex items-center justify-between text-sm text-gray-600 p-2 bg-gray-50 rounded-md mb-1'
          >
            <div className='flex items-center gap-2'>
              <span>{file.name}</span>
            </div>
            <button
              type='button'
              onClick={() => removeFile(index, fileType)}
              className='text-red-500 hover:text-red-700'
              disabled={uploading}
            >
              <X className='h-4 w-4' />
            </button>
          </div>
        );
      });
    }

    const reuploadedFileNames = files.map(file => file.name);
    const pendingMetadata = metadata.filter(
      meta => !reuploadedFileNames.includes(meta.name)
    );

    if (pendingMetadata.length > 0) {
      return pendingMetadata.map((file, index) => (
        <div
          key={`metadata-${index}`}
          className='flex items-center justify-between text-sm text-gray-600 p-2 bg-gray-50 rounded-md mb-1'
        >
          <div className='flex items-center gap-2'>
            <span>{file.name}</span>
          </div>
          <div className='text-amber-500'>
            <AlertCircle
              className='h-4 w-4'
              title='File needs to be re-uploaded'
            />
          </div>
        </div>
      ));
    }

    return null;
  };

  return (
    <div className='space-y-6'>
      {}
      <div
        id='idProof-section'
        className={`border-b pb-4 ${validationErrors.idProofFiles ? 'border-red-300' : ''}`}
      >
        <div className='flex justify-between items-center mb-2'>
          <h3 className='text-gray-700 font-medium'>
            ID Proof <span className='text-red-500'>*</span>
          </h3>
          <label
            htmlFor='id-proof-upload'
            className={`flex items-center gap-1 ${uploading ? 'text-gray-400 cursor-not-allowed' : 'text-nursery-blue cursor-pointer'}`}
          >
            <FilePlus2 className='h-5 w-5' />
            <span>Add Files</span>
          </label>
          <input
            id='id-proof-upload'
            type='file'
            className='hidden'
            multiple
            accept='image/*,.pdf,.doc,.docx'
            onChange={e => handleFileChange(e, 'idProofFiles')}
            disabled={uploading}
          />
        </div>

        {renderFileList(formData.idProofFiles, 'idProofFiles')}

        {validationErrors.idProofFiles && (
          <div className='mt-1 text-red-500 text-sm flex items-center'>
            <AlertCircle className='h-4 w-4 mr-1' />
            <span>At least one ID proof is required</span>
          </div>
        )}

        {formData.idProofFiles.length === 0 &&
          fileMetadata.idProofFilesMetadata?.length > 0 && (
            <div className='mt-1 text-amber-500 text-sm flex items-center'>
              <AlertCircle className='h-4 w-4 mr-1' />
              <span>
                You uploaded this file earlier. Please try uploading it again
              </span>
            </div>
          )}
      </div>

      {}
      <div
        id='experienceProof-section'
        className={`border-b pb-4 ${validationErrors.experienceProofFiles ? 'border-red-300' : ''}`}
      >
        <div className='flex justify-between items-center mb-2'>
          <h3 className='text-gray-700 font-medium'>
            Experience Proof <span className='text-red-500'>*</span>
          </h3>
          <label
            htmlFor='experience-proof-upload'
            className={`flex items-center gap-1 ${uploading ? 'text-gray-400 cursor-not-allowed' : 'text-nursery-blue cursor-pointer'}`}
          >
            <FilePlus2 className='h-5 w-5' />
            <span>Add Files</span>
          </label>
          <input
            id='experience-proof-upload'
            type='file'
            className='hidden'
            multiple
            accept='image/*,.pdf,.doc,.docx'
            onChange={e => handleFileChange(e, 'experienceProofFiles')}
            disabled={uploading}
          />
        </div>

        {renderFileList(formData.experienceProofFiles, 'experienceProofFiles')}

        {validationErrors.experienceProofFiles && (
          <div className='mt-1 text-red-500 text-sm flex items-center'>
            <AlertCircle className='h-4 w-4 mr-1' />
            <span>At least one experience proof is required</span>
          </div>
        )}

        {formData.experienceProofFiles.length === 0 &&
          fileMetadata.experienceProofFilesMetadata?.length > 0 && (
            <div className='mt-1 text-amber-500 text-sm flex items-center'>
              <AlertCircle className='h-4 w-4 mr-1' />
              <span>
                You uploaded this file earlier. Please try uploading it again
              </span>
            </div>
          )}
      </div>

      {}
      <div id='otherDocument-section' className='border-b pb-4'>
        <div className='flex justify-between items-center mb-2'>
          <h3 className='text-gray-700 font-medium'>Other Documents {}</h3>
          <label
            htmlFor='other-documents-upload'
            className={`flex items-center gap-1 ${uploading ? 'text-gray-400 cursor-not-allowed' : 'text-nursery-blue cursor-pointer'}`}
          >
            <FilePlus2 className='h-5 w-5' />
            <span>Add Files</span>
          </label>
          <input
            id='other-documents-upload'
            type='file'
            className='hidden'
            multiple
            accept='image/*,.pdf,.doc,.docx'
            onChange={e => handleFileChange(e, 'otherDocumentFiles')}
            disabled={uploading}
          />
        </div>

        {renderFileList(formData.otherDocumentFiles, 'otherDocumentFiles')}

        {formData.otherDocumentFiles.length === 0 &&
          fileMetadata.otherDocumentFilesMetadata?.length > 0 && (
            <div className='mt-1 text-amber-500 text-sm flex items-center'>
              <AlertCircle className='h-4 w-4 mr-1' />
              <span>
                You uploaded this file earlier. Please try uploading it again
              </span>
            </div>
          )}
      </div>

      {}
      {uploading && (
        <div className='flex items-center justify-center text-nursery-blue py-2'>
          <Loader2 className='h-5 w-5 mr-2 animate-spin' />
          <span>Uploading documents...</span>
        </div>
      )}

      {}
      <div className='flex justify-between pt-4'>
        <Button
          onClick={handleBack}
          variant='outline'
          className='w-24'
          disabled={uploading}
        >
          Back
        </Button>
        <Button
          onClick={handleFormSubmit}
          className='min-w-24 bg-nursery-blue'
          disabled={uploading}
        >
          {uploading ? (
            <div className='flex items-center'>
              <Loader2 className='h-4 w-4 mr-1 animate-spin' />
              <span>Uploading</span>
            </div>
          ) : (
            'Submit'
          )}
        </Button>
      </div>
    </div>
  );
};

export default DocumentUploadForm;
