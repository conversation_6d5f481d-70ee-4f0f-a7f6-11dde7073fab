import React from 'react';
import { MessageCircle, MoreVertical } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useBookingActions } from '@/hooks/useBookingActions';
import { Booking } from '@/store/api/customerApiSlice';

interface BookingActionsProps {
  booking: Booking;
  currentServiceStatus: string;
  isAccepted: boolean;
  updatingStatus: boolean;
  updatingServiceStatus: boolean;
  onAcceptBooking: () => void;
  onDeclineBooking: () => void;
  onStartService: () => void;
  onEndService: () => void;
  onPaymentReceived: () => void;
  expandedBookings?: Set<string>;
  onToggleExpand?: (bookingId: string) => void;
}

const BookingActions: React.FC<BookingActionsProps> = ({
  booking,
  currentServiceStatus,
  isAccepted,
  updatingStatus,
  updatingServiceStatus,
  onAcceptBooking,
  onDeclineBooking,
  onStartService,
  onEndService,
  onPaymentReceived,
  expandedBookings,
  onToggleExpand,
}) => {
  const navigate = useNavigate();
  const { canExpand } = useBookingActions();

  const isExpanded = expandedBookings?.has(booking.booking_id) || false;

  const renderServiceActionButton = () => {
    if (booking.booking_status !== 'Accepted') return null;

    if (currentServiceStatus === 'not_started') {
      return (
        <button
          className='flex-1 bg-nursery-blue text-white hover:bg-opacity-85 disabled:opacity-50 px-4 py-2 rounded-lg font-semibold transition-colors'
          onClick={e => {
            e.stopPropagation();
            onStartService();
          }}
          disabled={updatingServiceStatus}
        >
          {updatingServiceStatus ? 'Starting...' : 'Start Service'}
        </button>
      );
    }

    if (currentServiceStatus === 'started') {
      return (
        <button
          className='flex-1 bg-nursery-blue text-white hover:bg-opacity-85 disabled:opacity-50 px-4 py-2 rounded-lg font-semibold transition-colors'
          onClick={e => {
            e.stopPropagation();
            onEndService();
          }}
          disabled={updatingServiceStatus}
        >
          {updatingServiceStatus ? 'Ending...' : 'End Service'}
        </button>
      );
    }

    return null;
  };

  const getMessageButtonClasses = () => {
    return currentServiceStatus === 'payment_received'
      ? 'text-gray-400 bg-gray-200 cursor-not-allowed'
      : 'text-[#FF8800] bg-[#FF88002E] shadow-lg';
  };

  const handleMessageClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (currentServiceStatus !== 'payment_received') {
      navigate('/chat');
    }
  };

  const getPaymentButtonText = () => {
    return updatingStatus || updatingServiceStatus
      ? 'Processing...'
      : 'Mark as Payment Received';
  };

  return (
    <div>
      {booking.booking_status === 'Pending' && (
        <div className='flex gap-3 md:w-auto w-full'>
          <button
            className='flex-1 bg-[#00A912] text-white hover:bg-green-700 disabled:opacity-50 px-5 py-2 rounded-lg font-semibold transition-colors'
            onClick={e => {
              e.stopPropagation();
              onAcceptBooking();
            }}
            disabled={updatingStatus}
          >
            {updatingStatus ? 'Processing...' : 'Accept'}
          </button>
          <button
            className='flex-1 bg-[#EB001B] text-white hover:bg-red-600 disabled:opacity-50 px-5 py-2 rounded-lg font-semibold transition-colors'
            onClick={e => {
              e.stopPropagation();
              onDeclineBooking();
            }}
            disabled={updatingStatus}
          >
            {updatingStatus ? 'Processing...' : 'Decline'}
          </button>
        </div>
      )}

      {isAccepted && (
        <div className='flex gap-6 md:w-auto w-full'>
          {renderServiceActionButton()}

          <button
            className={`flex gap-1 items-center px-4 py-2 rounded-lg font-semibold transition-colors ${getMessageButtonClasses()}`}
            onClick={handleMessageClick}
            disabled={currentServiceStatus === 'payment_received'}
          >
            <MessageCircle className='w-5 h-5' />
            Message
          </button>

          {canExpand(booking) && onToggleExpand && (
            <button
              onClick={e => {
                e.stopPropagation();
                onToggleExpand(booking.booking_id);
              }}
              className='flex gap-1 items-center px-4 py-2 rounded-lg font-semibold text-[#7039D6] bg-[#7039D62E] transition-colors shadow-lg'
            >
              <MoreVertical className='w-5 h-5' />
              {isExpanded ? 'View Less' : 'View More'}
            </button>
          )}
        </div>
      )}

      {booking.booking_status === 'Completed' &&
        currentServiceStatus === 'completed' && (
          <div className='flex justify-center mt-4 mb-2'>
            <button
              onClick={e => {
                e.stopPropagation();
                onPaymentReceived();
              }}
              disabled={updatingStatus || updatingServiceStatus}
              className='w-full bg-nursery-blue hover:bg-opacity-85 disabled:opacity-50 text-white py-2 px-4 rounded-lg font-semibold transition-colors'
            >
              {getPaymentButtonText()}
            </button>
          </div>
        )}
    </div>
  );
};

export default BookingActions;
