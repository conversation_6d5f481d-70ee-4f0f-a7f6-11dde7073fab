import React from 'react';
import { Button } from '@/components/ui/button';
import { ChevronDown, MapPin, User } from 'lucide-react';
import home from '../../public/Images/Home.svg';
import { useNavigate } from 'react-router-dom';
import { toast, toastUtils } from '@/utils/toast';
import { formatTime12Hour } from '@/utils/dateTime';
import { useBookingActions } from '@/hooks/useBookingActions';
import DirectionsButton from '@/components/booking/DirectionsButton';
import DeclineBookingForm from '@/components/DeclineBookingForm';

const BookingRequests = ({
  bookingResponse,
  bookingLoading,
  bookingError,
  bookingRetryAttempts,
  handleBookingRetry,
  handleBookingStatus,
  updatingStatus,
}) => {
  const navigate = useNavigate();

  const bookingActions = useBookingActions({
    onUpdateBookingStatus: handleBookingStatus,
  });

  const handleDeclineBooking = (bookingId, event) => {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    bookingActions.openDeclineForm(bookingId);
  };

  const formatDate = dateString => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const pendingBookings =
    bookingResponse?.bookings?.filter(
      booking => booking.booking_status === 'Pending'
    ) || [];

  const sortedPendingBookings = [...pendingBookings].sort(
    (a, b) =>
      new Date(b.booked_date).getTime() - new Date(a.booked_date).getTime()
  );

  const latestPendingBooking = sortedPendingBookings[0];

  return (
    <div className='pt-5'>
      <div className='flex justify-between items-center mb-3'>
        <div className='flex items-center gap-2'>
          <h3 className='text-xl font-semibold text-gray-800'>
            Booking Requests
          </h3>
          {pendingBookings.length > 0 && (
            <span className='text-gray-800 text-lg font-semibold'>
              ( {pendingBookings.length} )
            </span>
          )}
        </div>
        {pendingBookings.length > 0 && (
          <button
            className='flex items-center gap-2 bg-nursery-blue px-3 py-1 rounded-full ml-2'
            onClick={() => navigate('/schedule')}
          >
            <span className='text-white text-sm font-semibold'>View All</span>
            <ChevronDown className='w-4 h-4 rotate-[-90deg] text-white' />
          </button>
        )}
      </div>
      {bookingLoading ? (
        <div className='bg-[#F2F2F2] p-7 items-center flex flex-1 flex-col justify-center rounded-xl gap-2'>
          <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-nursery-darkBlue'></div>
          <p className='text-base font-medium text-slate-500'>
            Loading bookings...
          </p>
        </div>
      ) : bookingError ? (
        <div className='bg-[#F2F2F2] p-7 items-center flex flex-1 flex-col justify-center rounded-xl gap-2'>
          <img src={home} alt='Error Bookings' className='p-2 w-16' />
          <p className='text-base font-medium text-red-500'>
            {bookingError?.status === 'FETCH_ERROR'
              ? 'Network error. Please check your connection.'
              : 'Failed to load bookings'}
          </p>
          <p className='text-xs text-gray-400 mb-2'>
            Attempts: {bookingRetryAttempts + 1}/3
          </p>
          <Button
            onClick={handleBookingRetry}
            className='mt-2 px-4 py-2 bg-nursery-darkBlue text-white rounded-md hover:bg-opacity-80'
            disabled={bookingLoading}
          >
            {bookingLoading ? 'Retrying...' : 'Retry'}
          </Button>
        </div>
      ) : bookingResponse && bookingResponse.total_bookings > 0 ? (
        <div className=''>
          {latestPendingBooking ? (
            <div
              key={latestPendingBooking.id}
              className='bg-[#F2F2F2] rounded-xl p-4 '
            >
              <div className='flex md:flex-row flex-col md:justify-start md:gap-8 gap-1 border-b border-gray-100'>
                <div className='items-center'>
                  <div className='text-sm text-gray-800 '>Booking ID</div>
                  <div className='font-semibold text-gray-900'>
                    #{latestPendingBooking.booking_id}
                  </div>
                </div>
                <div>
                  <div className='text-sm text-gray-800 '>Service Date</div>
                  <div className='font-semibold text-gray-900'>
                    {formatDate(latestPendingBooking.booked_date)}
                  </div>
                </div>
                <div>
                  <div className='text-sm text-gray-800 '>Service Time</div>
                  <div className='font-semibold text-gray-900'>
                    {formatTime12Hour(latestPendingBooking.booked_slot)}
                  </div>
                </div>

                <div className='mb-4'>
                  <div className='text-sm text-gray-800 mb-1'>Service Type</div>
                  <div className='font-semibold text-gray-900'>
                    {latestPendingBooking.services_selected
                      ? typeof latestPendingBooking.services_selected ===
                        'string'
                        ? JSON.parse(
                            latestPendingBooking.services_selected
                          ).join(', ')
                        : Array.isArray(latestPendingBooking.services_selected)
                          ? latestPendingBooking.services_selected.join(', ')
                          : latestPendingBooking.services_selected
                      : ''}
                  </div>
                </div>
              </div>

              <div className='flex md:flex-row flex-col justify-between items-center '>
                <div>
                  <div className='mb-2'>
                    <div className='text-sm text-gray-800 mb-2'>Patient</div>
                    <div className='flex items-center gap-3'>
                      <div className='w-10 h-10 bg-nursery-blue rounded-full flex items-center justify-center'>
                        <User className='w-6 h-6 text-white' />
                      </div>
                      <div>
                        <div className='font-semibold text-gray-900 text-lg'>
                          {latestPendingBooking.customer_given_name}
                        </div>
                      </div>
                      <div className=''>
                        {latestPendingBooking.booking_status !== 'Pending' && (
                          <div className='flex '>
                            <span
                              className={`px-3 py-1 rounded-full text-sm font-semibold ${
                                latestPendingBooking.booking_status ===
                                'Accepted'
                                  ? 'bg-[#00A912] text-white'
                                  : latestPendingBooking.booking_status ===
                                      'Declined'
                                    ? 'bg-[#EB001B] text-white'
                                    : 'bg-gray-100 text-gray-800'
                              }`}
                            >
                              {latestPendingBooking.booking_status}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className=''>
                    <div className='flex items-start gap-2 mb-2'>
                      <MapPin className='w-5 h-5 text-nursery-darkBlue mt-0.5 flex-shrink-0' />
                      <div className='text-sm text-gray-800'>
                        {latestPendingBooking.customer_booked_location_address}
                      </div>
                    </div>
                    {latestPendingBooking.booking_status === 'Pending' && (
                      <div className='mb-3'>
                        <DirectionsButton booking={latestPendingBooking} />
                      </div>
                    )}
                  </div>
                </div>

                <div>
                  {latestPendingBooking.booking_status === 'Pending' && (
                    <div className='flex gap-3 w-1/4'>
                      <button
                        className='flex-1 bg-[#00A912] text-white hover:bg-green-700 disabled:opacity-50 px-5 py-2 rounded-lg font-semibold transition-colors'
                        onClick={() => {
                          handleBookingStatus(
                            latestPendingBooking.booking_id,
                            'Accepted'
                          )
                            .then(() => {
                              toastUtils.successAlt(
                                'Booking accepted successfully!'
                              );
                            })
                            .catch((_error: unknown) => {
                              toast.error(
                                'Failed to accept booking. Please try again.'
                              );
                            });
                        }}
                        disabled={
                          updatingStatus ||
                          bookingActions.declineForm.isSubmitting
                        }
                      >
                        {updatingStatus ? 'Processing...' : 'Accept'}
                      </button>
                      <button
                        className='flex-1 bg-[#EB001B] text-white hover:bg-red-600 disabled:opacity-50 px-5 py-2 rounded-lg font-semibold transition-colors'
                        onClick={e =>
                          handleDeclineBooking(
                            latestPendingBooking.booking_id,
                            e
                          )
                        }
                        disabled={
                          updatingStatus ||
                          bookingActions.declineForm.isSubmitting
                        }
                      >
                        {updatingStatus ? 'Processing...' : 'Decline'}
                      </button>
                    </div>
                  )}
                </div>
              </div>

              <DeclineBookingForm
                isOpen={bookingActions.declineForm.isOpen}
                declineReason={bookingActions.declineForm.reason}
                setDeclineReason={bookingActions.updateDeclineReason}
                declineReasonError={bookingActions.declineForm.error}
                setDeclineReasonError={() => {}}
                isSubmittingDecline={bookingActions.declineForm.isSubmitting}
                onSubmit={() =>
                  bookingActions.submitDecline(latestPendingBooking.booking_id)
                }
                onCancel={bookingActions.closeDeclineForm}
              />
            </div>
          ) : (
            <div className='bg-slate-100 p-7 items-center flex flex-1 flex-col justify-center rounded-xl gap-2'>
              <img src={home} alt='No Bookings' className='p-2 w-16' />
              <p className='text-base font-medium text-slate-500'>
                No Booking Requests for You
              </p>
            </div>
          )}
        </div>
      ) : (
        <div className='bg-slate-100 p-7 items-center flex flex-1 flex-col justify-center rounded-xl gap-2'>
          <img src={home} alt='No Bookings' className='p-2 w-16' />
          <p className='text-base font-medium text-slate-500'>
            No Booking Requests for You
          </p>
        </div>
      )}
    </div>
  );
};

export default BookingRequests;
