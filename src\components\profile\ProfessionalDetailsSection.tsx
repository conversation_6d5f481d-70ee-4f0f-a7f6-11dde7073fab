import React, { useEffect } from 'react';
import { ChevronDown, ChevronUp } from 'lucide-react';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import {
  useUpdateNurseProfessionalDetailsMutation,
  useGetProfileDetailsQuery,
} from '../../store/api/apiSlice';
import { toast } from '@/utils/toast';

interface ProfessionalDetailsSectionProps {
  expanded: boolean;
  setExpanded: React.Dispatch<React.SetStateAction<boolean>>;

  about: string;
  setAbout: React.Dispatch<React.SetStateAction<string>>;

  totalExperience: number;
  setTotalExperience: React.Dispatch<React.SetStateAction<number>>;

  emergencyContact: string;
  setEmergencyContact: React.Dispatch<React.SetStateAction<string>>;

  servicesProvided: string[];
  setServicesProvided: React.Dispatch<React.SetStateAction<string[]>>;
}

const ProfessionalDetailsSection = ({
  expanded,
  setExpanded,
  about,
  setAbout,
  totalExperience,
  setTotalExperience,
  servicesProvided,
  setServicesProvided,
  emergencyContact,
  setEmergencyContact,
}: ProfessionalDetailsSectionProps) => {
  const { data: currentDetails, refetch } = useGetProfileDetailsQuery(
    undefined,
    {
      skip: false,
    }
  );

  useEffect(() => {
    if (currentDetails) {
      setAbout(currentDetails?.details?.about || '');
      setTotalExperience(currentDetails?.details?.total_years_of_exp || 0);
      setEmergencyContact(currentDetails?.details?.emergency_contact || '');

      const services = currentDetails?.details?.service_provide;
      setServicesProvided(Array.isArray(services) ? services : []);
    }
  }, [
    currentDetails,
    setAbout,
    setEmergencyContact,
    setServicesProvided,
    setTotalExperience,
  ]);

  const [
    updateProfessionalDetails,
    { isLoading, isSuccess: _isSuccess, isError, error },
  ] = useUpdateNurseProfessionalDetailsMutation();

  useEffect(() => {
    if (isError) {
      toast.error('Failed to update details. Please try again.');
      console.error('Update error:', error);
    }
  }, [isError, error]);

  const handleUpdateProfile = async () => {
    try {
      const professionalData = {
        total_years_of_exp: totalExperience,
        emergency_contact: emergencyContact,
        about: about,
        service_provide: servicesProvided,
      };

      const result = await updateProfessionalDetails(professionalData).unwrap();

      if (result.success) {
        toast.success('Professional details updated successfully!');

        refetch();
      } else {
        toast.error(result.message || 'Update failed. Please try again.');
      }
    } catch (err) {
      console.error('Failed to update profile:', err);
      toast.error('Update failed. Please check your connection and try again.');
    }
  };
  return (
    <div className='bg-[#F2F2F2] rounded-lg shadow-lg p-5'>
      <Collapsible
        open={expanded}
        onOpenChange={setExpanded}
        className='w-full'
      >
        <CollapsibleTrigger asChild className='w-full'>
          <div className='flex justify-between items-center cursor-pointer py-2'>
            <h2 className='text-xl font-bold'>Professional Details</h2>
            {expanded ? (
              <ChevronUp className='h-6 w-6 text-gray-400' />
            ) : (
              <ChevronDown className='h-6 w-6 text-gray-400' />
            )}
          </div>
        </CollapsibleTrigger>

        <CollapsibleContent className='space-y-4 mt-2'>
          <div>
            <label className='text-gray-800 text-sm'>
              Total Experience (in Years as a Nurse)
            </label>
            <Input
              type='number'
              className='mt-1'
              value={totalExperience ?? ''}
              onChange={e => setTotalExperience(Number(e.target.value))}
              placeholder='0'
            />
          </div>

          <div>
            <label className='text-gray-800 text-sm'>
              Emergency Contact Number
            </label>
            <Input
              type='tel'
              placeholder='Enter emergency contact'
              className='mt-1'
              value={emergencyContact}
              onChange={e => setEmergencyContact(e.target.value)}
            />
          </div>

          <div>
            <label className='text-gray-800 text-sm'>About</label>
            <Textarea
              value={about || ''}
              onChange={e => setAbout(e.target.value)}
              className='mt-1 min-h-[150px]'
              placeholder='Your Professional Summary...'
            />
          </div>

          <div>
            <label className='text-gray-800 text-sm'>
              Nursing Services Offered
            </label>

            {servicesProvided && servicesProvided.length > 0 && (
              <div className='flex flex-wrap gap-2 mt-2'>
                {servicesProvided.map((service, index) => (
                  <div
                    key={`service-${service}-${index}`}
                    className='bg-nursery-mint text-nursery-navy px-3 py-2 rounded-full text-sm'
                  >
                    {service}
                  </div>
                ))}
              </div>
            )}
          </div>

          <Button
            className='bg-nursery-darkBlue hover:bg-[#1e6880] text-white hover:shadow-xl transition-colors duration-300 ease-in-out'
            onClick={handleUpdateProfile}
            disabled={isLoading}
          >
            {isLoading ? 'Updating...' : 'Update Profile'}
          </Button>
        </CollapsibleContent>
      </Collapsible>
    </div>
  );
};

export default ProfessionalDetailsSection;
