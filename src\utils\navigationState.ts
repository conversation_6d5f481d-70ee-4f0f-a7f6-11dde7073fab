export interface NavigationState {
  username: string;
  given_name: string;
  address: string;
  user_id: string;
  nurse_set_location: boolean;
}

interface ProfileDetails {
  id: number;
  user_id: string;
  date_of_birth: string;
  gender: string;
  emergency_contact: string | '';
  total_years_of_exp: number;
  service_provide: string[];
  created_at: string;
  updated_at: string;
  phone_number: string;
  given_name: string;
  family_name: string;
  email: string;
  longitude: number | '';
  latitude: number | '';
  address: string | '';
  about: string | '';
}

export const createNavigationState = (
  profileDetails: ProfileDetails
): NavigationState => {
  return {
    username: profileDetails.email,
    given_name: profileDetails.given_name,
    address: profileDetails.address,
    user_id: profileDetails.user_id,
    nurse_set_location: true,
  };
};

export const createNavigationStateFromParams = (
  username: string,
  givenName: string,
  address: string,
  userId: string
): NavigationState => {
  return {
    username,
    given_name: givenName,
    address,
    user_id: userId,
    nurse_set_location: true,
  };
};

export const createNavigationStateWithFallback = (
  profileDetails: ProfileDetailsResponse | null | undefined,
  fallbackData?: {
    username?: string;
    givenName?: string;
    address?: string;
    userId?: string;
  }
): NavigationState | null => {
  if (!profileDetails?.details) {
    return null;
  }

  return {
    username: profileDetails.details.email || fallbackData?.username || '',
    given_name:
      profileDetails.details.given_name || fallbackData?.givenName || 'User',
    address: profileDetails.details.address || fallbackData?.address || '',
    user_id: profileDetails.details.user_id || fallbackData?.userId || '',
    nurse_set_location: true,
  };
};
