import React from 'react';
import { Button } from '@/components/ui/button';
import { AlertTriangle, CheckCircle, Calendar, Clock } from 'lucide-react';

interface AvailabilitySummaryProps {
  selectedDate: Date | null;
  selectedDateKey: string | null;
  selectedDateNewSlots: string[];
  selectedDateExistingSlots: string[];
  timeSlots: string[];
  isSlotAlreadyBooked: (dateKey: string, timeSlot: string) => boolean;
  toggleSlot: (dateKey: string, timeSlot: string) => void;
  toggleAllSlots: (dateKey: string) => void;
  isCreating: boolean;
  newlySelectedSlots: Record<string, string[]>;
  fare: string;
  fareError: string;
  handleSaveAvailability: () => void;
  totalNewSlots: number;
  totalNewDays: number;
  potentialEarnings: string;
  existingSlots: Record<string, string[]>;
}

const AvailabilitySummary: React.FC<AvailabilitySummaryProps> = ({
  selectedDate,
  selectedDateKey,
  selectedDateNewSlots,
  selectedDateExistingSlots,
  timeSlots,
  isSlotAlreadyBooked,
  toggleSlot,
  toggleAllSlots,
  isCreating,
  newlySelectedSlots,
  fare,
  fareError,
  handleSaveAvailability,
  totalNewSlots,
  totalNewDays,
  potentialEarnings,
  existingSlots,
}) => {
  const getSlotButtonClassName = (
    isExisting: boolean,
    isNewlySelected: boolean
  ): string => {
    const baseClasses =
      'p-3 rounded-lg border-2 transition-colors text-sm font-medium items-center relative';

    if (isExisting) {
      return `${baseClasses} bg-green-200 border-green-300 text-green-800`;
    }

    if (isNewlySelected) {
      return `${baseClasses} bg-nursery-blue text-white border-nursery-blue`;
    }

    return `${baseClasses} bg-gray-50 border-gray-200 text-gray-600 hover:bg-gray-200`;
  };

  return (
    <>
      {}
      <section className='rounded-lg'>
        <div className='bg-[#F2F2F2] rounded-lg shadow-xl border p-6'>
          {selectedDate ? (
            <>
              <div className='flex items-center justify-between mb-4'>
                <h3 className='text-lg font-semibold'>
                  Time Slots for {selectedDate.toLocaleDateString()}
                </h3>
                <Button
                  onClick={() =>
                    selectedDateKey && toggleAllSlots(selectedDateKey)
                  }
                  variant='outline'
                  size='sm'
                  disabled={isCreating}
                >
                  {selectedDateNewSlots.length ===
                  timeSlots.filter(
                    slot => !isSlotAlreadyBooked(selectedDateKey || '', slot)
                  ).length
                    ? 'Clear All'
                    : 'Select All Available'}
                </Button>
              </div>
              <div className='grid grid-cols-2 gap-3'>
                {timeSlots.map(timeSlot => {
                  const isExisting =
                    selectedDateExistingSlots.includes(timeSlot);
                  const isNewlySelected =
                    selectedDateNewSlots.includes(timeSlot);

                  return (
                    <button
                      key={timeSlot}
                      onClick={() =>
                        selectedDateKey && toggleSlot(selectedDateKey, timeSlot)
                      }
                      disabled={isCreating || isExisting}
                      className={getSlotButtonClassName(
                        isExisting,
                        isNewlySelected
                      )}
                    >
                      <Clock className='h-4 w-4 inline mr-1' />
                      {timeSlot}
                      {isExisting && (
                        <CheckCircle className='h-4 w-4 inline ml-1 text-green-600' />
                      )}
                    </button>
                  );
                })}
              </div>
              {}
              <div className='mt-4 space-y-2'>
                {selectedDateExistingSlots.length > 0 && (
                  <div className='p-2 bg-green-50 border border-green-200 rounded-sm'>
                    <p className='text-sm text-green-700'>
                      <strong>Existing slots:</strong>{' '}
                      {selectedDateExistingSlots.length} already booked
                    </p>
                  </div>
                )}
                {selectedDateNewSlots.length > 0 && (
                  <div className='p-2 bg-nursery-blue bg-opacity-40 rounded-sm'>
                    <p className='text-sm text-nursery-darkBlue'>
                      <strong>New selections:</strong>{' '}
                      {selectedDateNewSlots.length} slots selected
                    </p>
                  </div>
                )}
              </div>
            </>
          ) : (
            <div className='text-center py-12 bg-[#F2F2F2] text-gray-500 rounded-lg'>
              <Calendar className='h-12 w-12 mx-auto mb-4 opacity-50' />
              <p>Select a date from the calendar to set available time slots</p>
              <p className='text-sm mt-2'>
                Green dots = existing slots, Blue dots = new selections
              </p>
            </div>
          )}
        </div>
      </section>

      {}
      {totalNewSlots > 0 && (
        <section className='mt-8 bg-[#F2F2F2] rounded-lg p-6 border '>
          <h2 className='text-lg font-semibold mb-4 text-nursery-darkBlue '>
            Selections Summary
          </h2>
          <div className='space-y-3'>
            {Object.entries(newlySelectedSlots)
              .sort(([a], [b]) => a.localeCompare(b))
              .map(([date, slots]) => {
                const [year, month, day] = date.split('-');
                const displayDate = new Date(
                  Number(year),
                  Number(month) - 1,
                  Number(day)
                );
                return (
                  <div
                    key={date}
                    className='flex flex-col items-start p-4 bg-white rounded-xl shadow-lg '
                  >
                    <div className='flex-1'>
                      <div className='font-medium text-nursery-darkBlue'>
                        {displayDate.toLocaleDateString('en-US', {
                          weekday: 'long',
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric',
                        })}
                      </div>
                      <div className='text-sm text-nursery-darkBlue mt-1'>
                        <strong>{slots.length} new slots:</strong>{' '}
                        {slots.join(', ')}
                      </div>
                    </div>
                    {fare && (
                      <div className='text-sm text-green-600 mt-2 font-medium'>
                        <strong>Potential earnings:</strong> ₹
                        {(parseFloat(fare) * slots.length).toFixed(2)}
                      </div>
                    )}
                  </div>
                );
              })}
          </div>
          <div className='mt-6 mx-auto max-w-xl rounded-xl bg-white shadow-xl p-6 text-center '>
            <div className='text-xl font-semibold text-nursery-darkBlue mb-2'>
              {totalNewSlots} New slots across {totalNewDays} day
              {totalNewDays !== 1 ? 's' : ''}
            </div>
            {fare && (
              <div className='text-nursery-darkBlue text-lg font-bold'>
                New potential earnings: ₹{potentialEarnings}
              </div>
            )}
          </div>
          {}
          <div className='flex flex-col items-center mt-8'>
            <Button
              className='min-w-[25%] bg-nursery-darkBlue hover:bg-nursery-darkBlue hover:opacity-80 text-white'
              onClick={handleSaveAvailability}
              disabled={totalNewSlots === 0 || isCreating}
            >
              {isCreating ? (
                <>
                  <div className='mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent'></div>
                  Saving...
                </>
              ) : (
                <>Save New Availability ({totalNewSlots} slots)</>
              )}
            </Button>
            {}
            {fareError && (
              <div className='mt-4 p-3 bg-red-50 border border-red-200 rounded-lg max-w-md'>
                <div className='flex items-start'>
                  <AlertTriangle className='h-4 w-4 text-red-600 mt-0.5 mr-2 flex-shrink-0' />
                  <p className='text-sm text-red-800'>{fareError}</p>
                </div>
              </div>
            )}
          </div>
        </section>
      )}

      {}
      {Object.keys(existingSlots).length > 0 && (
        <section className='mt-8 bg-[#F2F2F2] rounded-lg p-6 border '>
          <div className='flex items-center mb-4'>
            <CheckCircle className='h-6 w-6 mr-2 text-[#00A912]' />
            <h2 className='text-lg font-semibold text-[#00A912]'>
              Your Available Slots
            </h2>
          </div>
          <div className='space-y-3'>
            {Object.entries(existingSlots)
              .filter(([date, _slots]) => {
                const today = new Date();
                today.setHours(0, 0, 0, 0);
                let slotDate;
                if (date.includes('-')) {
                  const parts = date.split('-');
                  if (parts.length === 2) {
                    const [month, day] = parts;
                    const currentYear = new Date().getFullYear();
                    slotDate = new Date(
                      currentYear,
                      Number(month) - 1,
                      Number(day)
                    );
                  } else if (parts.length === 3) {
                    const [year, month, day] = parts;
                    slotDate = new Date(
                      Number(year),
                      Number(month) - 1,
                      Number(day)
                    );
                  }
                }
                return slotDate && slotDate >= today;
              })
              .sort(([a], [b]) => a.localeCompare(b))
              .map(([date, slots]) => {
                let displayDate;
                if (date.includes('-')) {
                  const parts = date.split('-');
                  if (parts.length === 2) {
                    const [month, day] = parts;
                    const currentYear = new Date().getFullYear();
                    displayDate = new Date(
                      currentYear,
                      Number(month) - 1,
                      Number(day)
                    );
                  } else if (parts.length === 3) {
                    const [year, month, day] = parts;
                    displayDate = new Date(
                      Number(year),
                      Number(month) - 1,
                      Number(day)
                    );
                  }
                }
                return (
                  <div
                    key={date}
                    className='flex flex-col items-start p-4 bg-white rounded-lg shadow-lg'
                  >
                    <div className='flex-1'>
                      <div className='font-semibold text-nursery-darkBlue '>
                        {displayDate?.toLocaleDateString('en-US', {
                          weekday: 'long',
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric',
                        })}
                      </div>
                      <div className='text-sm text-nursery-darkBlue mt-1'>
                        <strong>{slots.length} Existing slots:</strong>{' '}
                        {slots.join(', ')}
                      </div>
                    </div>
                  </div>
                );
              })}
          </div>
        </section>
      )}
    </>
  );
};

export default AvailabilitySummary;
