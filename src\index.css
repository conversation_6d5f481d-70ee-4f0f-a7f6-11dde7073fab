@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 196 41% 50%;
    --primary-foreground: 210 40% 98%;

    --secondary: 196 41% 96.1%;
    --secondary-foreground: 196 41% 35%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 196 41% 96.1%;
    --accent-foreground: 196 41% 35%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 196 41% 50%;

    --radius: 0.75rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings:
      'rlig' 1,
      'calt' 1;
  }
}

@layer components {
  .nursery-button {
    @apply relative overflow-hidden transform transition-all duration-300 ease-in-out;
  }

  .nursery-button::before {
    @apply content-[''] absolute top-0 left-0 w-full h-full bg-white/20 transform scale-x-0 origin-right transition-transform duration-300 ease-out;
  }

  .nursery-button:hover::before {
    @apply scale-x-100 origin-left;
  }

  .staggered-fade-in > * {
    opacity: 0;
    animation: fade-in 0.5s ease-out forwards;
  }

  .staggered-fade-in > *:nth-child(1) {
    animation-delay: 0.1s;
  }
  .staggered-fade-in > *:nth-child(2) {
    animation-delay: 0.2s;
  }
  .staggered-fade-in > *:nth-child(3) {
    animation-delay: 0.3s;
  }
  .staggered-fade-in > *:nth-child(4) {
    animation-delay: 0.4s;
  }
  .staggered-fade-in > *:nth-child(5) {
    animation-delay: 0.5s;
  }

  .glass-card {
    @apply relative backdrop-blur-sm bg-white/60 shadow-md rounded-2xl overflow-hidden;
  }

  .glass-card::before {
    @apply content-[''] absolute inset-0 bg-gradient-to-br from-white/50 to-white/10 z-0;
  }
}
