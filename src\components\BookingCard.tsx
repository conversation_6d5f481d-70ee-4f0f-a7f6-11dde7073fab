import React, { useState, useRef, useEffect } from 'react';
import ScheduleBookingDetails from '../pages/ScheduleBookingDetails';
import DeclineBookingForm from './DeclineBookingForm';
import BookingHeader from './booking/BookingHeader';
import BookingDetails from './booking/BookingDetails';
import CustomerInfo from './booking/CustomerInfo';
import BookingActions from './booking/BookingActions';
import { useBookingActions } from '@/hooks/useBookingActions';

const BookingCard = ({
  booking,
  index: _index,
  serviceStatus,
  setServiceStatus,
  onUpdateBookingStatus,
  onUpdateServiceStatus,
  updatingStatus,
  updatingServiceStatus,
  expandedBookings,
  setExpandedBookings,
  selectedTab: _selectedTab,
  setSelectedTab,
}) => {
  const [activeMenuId, setActiveMenuId] = useState(null);
  const isExpanded = expandedBookings?.has(booking.booking_id) || false;
  const menuRef = useRef(null);

  const currentServiceStatus =
    serviceStatus[booking.booking_id]?.status || 'not_started';
  const isAccepted =
    booking.booking_status === 'Accepted' ||
    booking.booking_status === 'Completed';

  const bookingActions = useBookingActions({
    onUpdateBookingStatus,
    onUpdateServiceStatus,
    setServiceStatus,
    setSelectedTab,
  });

  const toggleExpand = bookingId => {
    setExpandedBookings(prev => {
      const newSet = new Set(prev || []);
      if (newSet.has(bookingId)) {
        newSet.delete(bookingId);
      } else {
        newSet.add(bookingId);
      }
      return newSet;
    });
  };

  useEffect(() => {
    const handleClickOutside = event => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setActiveMenuId(null);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div className='bg-[#F2F2F2] p-4 rounded-xl mb-4'>
      <div className='p-1 rounded-lg relative'>
        <BookingHeader
          booking={booking}
          currentServiceStatus={currentServiceStatus}
          isAccepted={isAccepted}
          activeMenuId={activeMenuId}
          setActiveMenuId={setActiveMenuId}
          menuRef={menuRef}
          onDeclineBooking={() =>
            bookingActions.declineBooking(booking.booking_id)
          }
          updatingStatus={updatingStatus}
          isSubmittingDecline={bookingActions.declineForm.isSubmitting}
        />

        <BookingDetails booking={booking} />

        <div className='flex md:flex-row flex-col justify-between items-start mt-1'>
          <CustomerInfo
            booking={booking}
            currentServiceStatus={currentServiceStatus}
          />

          <BookingActions
            booking={booking}
            currentServiceStatus={currentServiceStatus}
            isAccepted={isAccepted}
            updatingStatus={updatingStatus}
            updatingServiceStatus={updatingServiceStatus}
            onAcceptBooking={() =>
              bookingActions.acceptBooking(booking.booking_id)
            }
            onDeclineBooking={() =>
              bookingActions.declineBooking(booking.booking_id)
            }
            onStartService={() =>
              bookingActions.startService(booking.booking_id)
            }
            onEndService={() => bookingActions.endService(booking.booking_id)}
            onPaymentReceived={() =>
              bookingActions.markPaymentReceived(booking.booking_id)
            }
            expandedBookings={expandedBookings}
            onToggleExpand={toggleExpand}
          />
        </div>

        <DeclineBookingForm
          isOpen={bookingActions.declineForm.isOpen}
          declineReason={bookingActions.declineForm.reason}
          setDeclineReason={bookingActions.updateDeclineReason}
          declineReasonError={bookingActions.declineForm.error}
          setDeclineReasonError={() => {}}
          isSubmittingDecline={bookingActions.declineForm.isSubmitting}
          onSubmit={() => bookingActions.submitDecline(booking.booking_id)}
          onCancel={bookingActions.closeDeclineForm}
        />
      </div>

      {bookingActions.canExpand(booking) && (
        <ScheduleBookingDetails
          booking={booking}
          isExpanded={isExpanded}
          serviceStatus={serviceStatus}
        />
      )}
    </div>
  );
};

export default BookingCard;
