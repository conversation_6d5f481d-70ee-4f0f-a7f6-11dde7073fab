import { useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, Edit2, User, Upload, X, Trash2 } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { toast } from '@/utils/toast';
import bg from '../../../public/Images/bg4.png';
import {
  useGetCurrentUserProfileImageQuery,
  useUploadProfileImageMutation,
  useDeleteProfileImageMutation,
} from '@/store/api/apiSlice';

interface ProfileHeaderProps {
  title: string;
}

const ProfileHeader = ({ title }: ProfileHeaderProps) => {
  const navigate = useNavigate();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  const { data: currentProfileImage, refetch } =
    useGetCurrentUserProfileImageQuery();
  const profileImage = currentProfileImage?.profile_image?.signed_url;
  const [uploadProfileImage, { isLoading: isUploading }] =
    useUploadProfileImageMutation();
  const [deleteProfileImage, { isLoading: isDeleting }] =
    useDeleteProfileImageMutation();

  const handleEditClick = () => {
    setIsModalOpen(true);
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const maxSize = 5 * 1024 * 1024;
    if (file.size > maxSize) {
      toast.error('File size exceeds 5MB limit');
      return;
    }

    const allowedTypes = [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif',
      'image/webp',
      'image/bmp',
    ];
    if (!allowedTypes.includes(file.type)) {
      toast.error(
        'Invalid file type. Only image files are allowed (JPEG, PNG, GIF, WebP, BMP)'
      );
      return;
    }

    setSelectedFile(file);

    const url = URL.createObjectURL(file);
    setPreviewUrl(url);
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      toast.error('Please select a file first');
      return;
    }

    const formData = new FormData();
    formData.append('image', selectedFile);

    try {
      const uploadPromise = uploadProfileImage(formData).unwrap();

      toast.promise(uploadPromise, {
        loading: 'Uploading image...',
        success: 'Profile image uploaded successfully!',
        error: (error: unknown) => {
          const errorObj = error as {
            data?: { error?: string };
            message?: string;
          };
          return `Upload failed: ${errorObj?.data?.error || errorObj?.message || 'Unknown error'}`;
        },
      });

      await uploadPromise;

      refetch();

      setIsModalOpen(false);
      setSelectedFile(null);
      setPreviewUrl(null);
    } catch (error) {
      console.error('Upload error:', error);
    }
  };

  const handleCancel = () => {
    setIsModalOpen(false);
    setSelectedFile(null);
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
      setPreviewUrl(null);
    }
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleChooseFile = () => {
    fileInputRef.current?.click();
  };

  const handleDeleteClick = () => {
    setIsDeleteModalOpen(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      const deletePromise = deleteProfileImage().unwrap();

      toast.promise(deletePromise, {
        loading: 'Deleting profile image...',
        success: 'Profile image deleted successfully!',
        error: (error: unknown) => {
          const errorObj = error as {
            data?: { error?: string };
            message?: string;
          };
          return `Delete failed: ${errorObj?.data?.error || errorObj?.message || 'Unknown error'}`;
        },
      });

      await deletePromise;

      refetch();
      setIsDeleteModalOpen(false);
    } catch (error) {
      console.error('Delete error:', error);
    }
  };

  const handleDeleteCancel = () => {
    setIsDeleteModalOpen(false);
  };

  return (
    <>
      <header className='relative w-full overflow-hidden text-white pt-5 pl-1 pb-10 flex flex-col shadow-lg'>
        <div className='absolute inset-0 w-full h-full z-0 bg-fixed'>
          <img
            src={bg}
            alt='Background Wallpaper'
            className='object-cover w-full'
          />
        </div>
        <div className='absolute inset-0 w-full object-cover bg-black bg-opacity-20 z-0' />

        <div className='relative z-10 h-full w-full flex items-center mb-2'>
          <button onClick={() => navigate(-1)} className='mr-4'>
            <ArrowLeft className='h-6 w-6' />
          </button>
          <h1 className='text-xl font-semibold'>{title}</h1>
        </div>

        <div className='flex justify-center mt-1 mb-5 '>
          <div className='relative'>
            <Avatar
              className={`${profileImage ? 'h-28 w-28 p-1' : 'h-16 w-16 p-2'} bg-white border-1 border-white shadow-lg`}
            >
              {profileImage ? (
                <AvatarImage
                  src={profileImage}
                  alt='Profile'
                  className='object-cover rounded-full'
                />
              ) : (
                <AvatarFallback className='bg-white text-nursery-blue text-2xl rounded-full'>
                  <User className='h-10 w-10' />
                </AvatarFallback>
              )}
            </Avatar>
            <button
              onClick={handleEditClick}
              className='absolute bottom-0 right-0 bg-nursery-blue rounded-full p-1 shadow-xl hover:bg-nursery-darkBlue transition-colors'
            >
              <Edit2 className='h-[18px] w-[18px] text-white' />
            </button>
            {profileImage && (
              <button
                onClick={handleDeleteClick}
                className='absolute bottom-0 left-0 bg-red-500 rounded-full p-1 shadow-xl hover:bg-red-600 transition-colors'
              >
                <Trash2 className='h-[18px] w-[18px] text-white' />
              </button>
            )}
          </div>
        </div>
      </header>

      {}
      {isModalOpen && (
        <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4'>
          <div className='bg-white rounded-lg shadow-xl max-w-md w-full p-6'>
            <div className='flex justify-between items-center mb-4'>
              <h2 className='text-lg font-semibold text-gray-900'>
                Upload Profile Image
              </h2>
              <button
                onClick={handleCancel}
                className='text-gray-400 hover:text-gray-600 transition-colors'
              >
                <X className='h-5 w-5' />
              </button>
            </div>

            <div className='space-y-4'>
              {}
              <input
                ref={fileInputRef}
                type='file'
                accept='image/*'
                onChange={handleFileSelect}
                className='hidden'
              />

              {}
              {previewUrl ? (
                <div className='space-y-3'>
                  <div className='flex justify-center'>
                    <img
                      src={previewUrl}
                      alt='Preview'
                      className='h-32 w-32 object-cover rounded-full border-2 border-gray-200'
                    />
                  </div>
                  <div className='text-sm text-gray-600 text-center'>
                    <p className='font-medium'>{selectedFile?.name}</p>
                    <p>
                      {((selectedFile?.size ?? 0) / (1024 * 1024)).toFixed(2)}{' '}
                      MB
                    </p>
                  </div>
                  <button
                    onClick={handleChooseFile}
                    className='w-full px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors'
                  >
                    Choose Different File
                  </button>
                </div>
              ) : (
                <div className='border-2 border-dashed border-gray-300 rounded-lg p-6 text-center'>
                  <Upload className='h-12 w-12 text-gray-400 mx-auto mb-4' />
                  <p className='text-sm text-gray-600 mb-2'>
                    Click to select an image file
                  </p>
                  <p className='text-xs text-gray-500 mb-4'>
                    Maximum file size: 5MB
                  </p>
                  <button
                    onClick={handleChooseFile}
                    className='px-4 py-2 bg-nursery-blue  text-white rounded-md text-sm font-medium hover:bg-nursery-darkBlue transition-colors'
                  >
                    Choose File
                  </button>
                </div>
              )}

              {}
              <div className='flex space-x-3 pt-4'>
                <button
                  onClick={handleCancel}
                  disabled={isUploading}
                  className='flex-1 px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed'
                >
                  Cancel
                </button>
                <button
                  onClick={handleUpload}
                  disabled={!selectedFile || isUploading}
                  className='flex-1 px-4 py-2 bg-nursery-blue text-white rounded-md text-sm font-medium hover:bg-nursery-darkBlue transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center'
                >
                  {isUploading ? (
                    <>
                      <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2'></div>
                      Uploading...
                    </>
                  ) : (
                    'Upload'
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {}
      {isDeleteModalOpen && (
        <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4'>
          <div className='bg-white rounded-lg shadow-xl max-w-md w-full p-6'>
            <div className='flex justify-between items-center mb-4'>
              <h2 className='text-lg font-semibold text-gray-900'>
                Delete Profile Image
              </h2>
              <button
                onClick={handleDeleteCancel}
                className='text-gray-600 hover:text-gray-800 transition-colors'
              >
                <X className='h-5 w-5' />
              </button>
            </div>

            <div className='space-y-4'>
              <div className='text-center'>
                <div className='mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4'>
                  <Trash2 className='h-6 w-6 text-red-600' />
                </div>
                <p className='text-sm text-gray-600 mb-2'>
                  Are you sure you want to delete your profile image?
                </p>
                <p className='text-xs text-gray-500'>
                  This action cannot be undone. Your profile will show the
                  default user icon.
                </p>
              </div>

              <div className='flex space-x-3 pt-4'>
                <button
                  onClick={handleDeleteCancel}
                  disabled={isDeleting}
                  className='flex-1 px-4 py-2 border border-gray-300 bg-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-400 transition-colors disabled:opacity-50 disabled:cursor-not-allowed'
                >
                  Cancel
                </button>
                <button
                  onClick={handleDeleteConfirm}
                  disabled={isDeleting}
                  className='flex-1 px-4 py-2 bg-nursery-blue text-white rounded-md text-sm font-medium hover:bg-red-600 transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center'
                >
                  {isDeleting ? (
                    <>
                      <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2'></div>
                      Deleting...
                    </>
                  ) : (
                    'Delete'
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default ProfileHeader;
