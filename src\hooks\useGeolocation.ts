import { useCallback } from 'react';
import { toast } from '@/utils/toast';
import { toast as sonnerToast } from 'sonner';

interface GeolocationOptions {
  enableHighAccuracy?: boolean;
  timeout?: number;
  maximumAge?: number;
}

interface UseGeolocationReturn {
  getDirections: (
    destinationLat: number,
    destinationLng: number,
    options?: GeolocationOptions
  ) => void;
}

export const useGeolocation = (): UseGeolocationReturn => {
  const getDirections = useCallback(
    (
      destinationLat: number,
      destinationLng: number,
      options: GeolocationOptions = {}
    ) => {
      if (isNaN(destinationLat) || isNaN(destinationLng)) {
        toast.error('Invalid location coordinates');
        console.error('Invalid coordinates:', {
          destinationLat,
          destinationLng,
        });
        return;
      }

      const fallbackUrl = `https://www.google.com/maps/dir/?api=1&destination=${destinationLat},${destinationLng}`;

      if (!navigator.geolocation) {
        window.open(fallbackUrl, '_blank');
        toast.error(
          'Geolocation is not supported by this browser. Opening directions without current location.'
        );
        return;
      }

      const loadingToast = toast.loading(
        'Getting your location for directions...'
      );
      if ('permissions' in navigator) {
        navigator.permissions
          .query({ name: 'geolocation' })
          .then(result => {
            if (result.state === 'denied') {
              sonnerToast.dismiss(loadingToast);
              toast.error(
                'Location access is denied. Please enable location services in your browser settings.'
              );
              window.open(fallbackUrl, '_blank');
              return;
            }
          })
          .catch(error => {
            console.error('Permission query not supported:', error);
          });
      }

      const defaultOptions: GeolocationOptions = {
        enableHighAccuracy: true,
        timeout: 60000,
        maximumAge: 0,
      };

      const finalOptions = { ...defaultOptions, ...options };

      const timeoutId = setTimeout(() => {
        sonnerToast.dismiss(loadingToast);
        toast.error(
          'Location request is taking longer than expected. Please wait or try again.'
        );
      }, finalOptions.timeout || 60000);

      navigator.geolocation.getCurrentPosition(
        position => {
          clearTimeout(timeoutId);

          const { latitude, longitude } = position.coords;

          const googleMapsUrl = `https://www.google.com/maps/dir/?api=1&origin=${latitude},${longitude}&destination=${destinationLat},${destinationLng}`;

          sonnerToast.dismiss(loadingToast);
          window.open(googleMapsUrl, '_blank');
          toast.success('Directions opened with your current location!');
        },
        error => {
          clearTimeout(timeoutId);

          console.error('Geolocation error:', error);
          sonnerToast.dismiss(loadingToast);

          let errorMessage = 'Unable to get your location. ';

          switch (error.code) {
            case error.PERMISSION_DENIED:
              errorMessage +=
                'Location access was denied. Please enable location services in your browser settings and try again. Opening directions without current location.';
              break;
            case error.POSITION_UNAVAILABLE:
              errorMessage +=
                'Location information is unavailable. Please check your device settings. Opening directions without current location.';
              break;
            case error.TIMEOUT:
              errorMessage +=
                'Location request timed out. Please try again or check your connection. Opening directions without current location.';
              break;
            default:
              errorMessage +=
                'An unknown error occurred. Opening directions without current location.';
              break;
          }

          toast.error(errorMessage);
          window.open(fallbackUrl, '_blank');
        },
        finalOptions
      );
    },
    []
  );

  return { getDirections };
};

export const extractBookingCoordinates = (booking: {
  customer_booked_location_latitude?: string;
  customer_booked_location_longitude?: string;
}): { lat: number; lng: number } | null => {
  const {
    customer_booked_location_latitude,
    customer_booked_location_longitude,
  } = booking;

  if (
    !customer_booked_location_latitude ||
    !customer_booked_location_longitude
  ) {
    toast.error('Location coordinates not available');
    return null;
  }

  const lat = parseFloat(customer_booked_location_latitude);
  const lng = parseFloat(customer_booked_location_longitude);

  if (isNaN(lat) || isNaN(lng)) {
    toast.error('Invalid location coordinates');
    console.error('Invalid coordinates:', { lat, lng });
    return null;
  }

  return { lat, lng };
};
