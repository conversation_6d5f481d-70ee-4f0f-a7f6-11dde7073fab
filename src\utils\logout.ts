import { store } from '@/store/store';
import { apiSlice } from '@/store/api/apiSlice';
import { customerApiSlice } from '@/store/api/customerApiSlice';

export const performLogout = () => {
  localStorage.clear();
  sessionStorage.clear();

  store.dispatch(apiSlice.util.resetApiState());
  store.dispatch(customerApiSlice.util.resetApiState());

  document.cookie.split(';').forEach(c => {
    document.cookie = c
      .replace(/^ +/, '')
      .replace(/=.*/, '=;expires=' + new Date().toUTCString() + ';path=/');
  });
};
