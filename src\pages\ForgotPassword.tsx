import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import Logo from '../components/onBoarding/Logo/Logo';
import { useForgotPasswordMutation } from '@/store/api/apiSlice';
import { toast } from '@/utils/toast';
import bg from '../../public/Images/bg4.png';

type RTKError = {
  status: number | string;
  data?: { error?: string; message?: string };
};

const isRTKError = (err: unknown): err is RTKError => {
  return typeof err === 'object' && err !== null && 'status' in err;
};

const validatePhoneNumber = (phoneNumber: string): string => {
  const numbersOnly = phoneNumber.replace('+91', '');

  if (!phoneNumber || !phoneNumber.trim()) {
    return 'Phone number is required.';
  }

  if (!/^[0-9]{10}$/.test(numbersOnly)) {
    return 'Please Enter a Valid 10-digit Mobile number.';
  }

  return '';
};

const handleForgotPasswordError = (error: unknown) => {
  console.error('Forgot password error:', error);

  if (
    isRTKError(error) &&
    (error.status === 404 || error.data?.error === 'User not found')
  ) {
    toast.error(
      'User not found. Please check your phone number or register first.'
    );
  } else if (
    isRTKError(error) &&
    error.status === 400 &&
    error.data?.error === 'Phone number is required'
  ) {
    toast.error('Phone number is required.');
  } else if (
    isRTKError(error) &&
    error.status === 400 &&
    error.data?.error === 'Invalid phone number format'
  ) {
    toast.error('Invalid phone number format. Please check and try again.');
  } else if (isRTKError(error) && error.status === 'FETCH_ERROR') {
    toast.error('Network error. Please check your connection and try again.');
  } else if (
    isRTKError(error) &&
    (error.status === 500 ||
      error.data?.error === 'Failed to process forgot password request')
  ) {
    toast.error('Failed to process request. Please try again later.');
  } else if (isRTKError(error) && error.data?.message) {
    toast.error(error.data.message);
  } else {
    toast.error('Failed to send OTP. Please try again.');
  }
};

interface FormButtonProps {
  type: 'submit' | 'button';
  variant?: 'primary' | 'outline';
  onClick?: () => void;
  disabled?: boolean;
  children: React.ReactNode;
}

const FormButton: React.FC<FormButtonProps> = ({
  type,
  variant = 'primary',
  onClick,
  disabled = false,
  children,
}) => {
  const baseClasses = 'w-full font-medium';
  const primaryClasses =
    'h-11 bg-[#5EB2CC] hover:bg-[#4996B5] text-white text-lg';
  const outlineClasses = 'h-10 border-[#4AB4CE] text-[#4AB4CE]';

  return (
    <Button
      type={type}
      variant={variant === 'outline' ? 'outline' : undefined}
      onClick={onClick}
      disabled={disabled}
      className={`${baseClasses} ${variant === 'primary' ? primaryClasses : outlineClasses}`}
    >
      {children}
    </Button>
  );
};

const ForgotPassword = () => {
  const navigate = useNavigate();
  const [phone_number, setPhoneNumber] = useState('');
  const [phoneNumberError, setPhoneNumberError] = useState<string>('');
  const [Forgot, { isLoading }] = useForgotPasswordMutation();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const validationError = validatePhoneNumber(phone_number);
    if (validationError) {
      setPhoneNumberError(validationError);
      return;
    } else {
      setPhoneNumberError('');
    }

    if (isLoading) {
      return;
    }

    try {
      const response = await Forgot({ phone_number }).unwrap();
      if (response) {
        toast.success(
          'Password reset code has been sent to your phone number.'
        );
        navigate('/otp-verify', { state: { phone_number } });
      }
    } catch (error: unknown) {
      handleForgotPasswordError(error);
    }
  };

  const handleMobileNumber = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value;

    if (!value.startsWith('+91')) {
      value = '+91' + value.replace(/^\+91/, '');
    }
    setPhoneNumber(value);

    if (phoneNumberError) {
      setPhoneNumberError('');
    }
  };

  return (
    <div className='relative w-full overflow-hidden min-h-screen flex flex-col'>
      <div className='absolute inset-0 w-full h-full z-0'>
        <img
          src={bg}
          alt='Background Wallpaper'
          className='w-full h-full object-cover'
        />
      </div>

      <div className='absolute inset-0 w-full object-cover bg-black bg-opacity-20 z-0' />

      <div className='relative z-10 h-full w-full flex-1 flex flex-col items-center justify-center pt-10 px-6 gap-3'>
        <Logo />

        {}
        <div className='w-full max-w-md bg-white rounded-xl p-6 shadow-lg'>
          <h2 className='text-2xl font-bold mb-6 text-nursery-blue'>
            Forgot Password
          </h2>

          <form onSubmit={handleSubmit} noValidate className='space-y-4'>
            <div>
              <Input
                type='text'
                placeholder='Enter Mobile Number'
                value={phone_number}
                onChange={handleMobileNumber}
                className='w-full h-11 text-base'
                required
              />
              {phoneNumberError && (
                <p className='text-sm text-red-500 mt-1'>{phoneNumberError}</p>
              )}
            </div>

            <FormButton type='submit' disabled={isLoading}>
              {isLoading ? 'Sending...' : 'Send OTP'}
            </FormButton>

            <FormButton
              type='button'
              variant='outline'
              onClick={() => navigate(-1)}
            >
              Go Back
            </FormButton>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ForgotPassword;
