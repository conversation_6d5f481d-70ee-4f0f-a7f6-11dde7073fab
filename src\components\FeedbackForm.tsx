import { useState, useEffect } from 'react';
import { useParams, useSearchParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { toast } from '@/utils/toast';
import axios from 'axios';
import { Star } from 'lucide-react';
import Logo from './onBoarding/Logo/Logo';

interface FeedbackFormData {
  name: string;
  rating: number;
  comments: string;
}

export function FeedbackForm() {
  const { feedbackId } = useParams();
  const [searchParams] = useSearchParams();
  const recipientName = searchParams.get('recipient');
  const nurseId = searchParams.get('nurseId');

  const [formData, setFormData] = useState<FeedbackFormData>({
    name: '',
    rating: 0,
    comments: '',
  });

  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [hoveredStar, setHoveredStar] = useState(0);
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    const feedbackSessions = JSON.parse(
      localStorage.getItem('feedbackSessions') || '{}'
    );
    if (feedbackSessions[feedbackId]?.status === 'submitted') {
      setIsSubmitted(true);
    }

    if (!nurseId) {
      toast.error('Invalid feedback link: Missing nurse ID', {
        style: { backgroundColor: '#FFFFFF', color: '#EF4444' },
        duration: 3000,
        dismissible: true,
      });
    }
  }, [feedbackId, nurseId]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }

    if (!formData.rating || formData.rating === 0) {
      newErrors.rating = 'Rating is required';
    }

    if (!formData.comments.trim()) {
      newErrors.comments = 'Comments are required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: string, value: string | number) => {
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }

    setFormData({ ...formData, [field]: value });
  };

  const getErrorMessage = (error: unknown): string => {
    const errorMap: Record<string, string> = {
      'Rating is required': 'Please provide a rating before submitting.',
      'Name is required': 'Please enter your name before submitting.',
      'Recipient name is required':
        'Recipient name is missing. Please contact support.',
      'Rating must be between 1 and 5':
        'Please select a rating between 1 and 5 stars.',
      'Failed to submit feedback':
        'Failed to submit feedback. Please try again later.',
    };

    const isAxiosError = (
      err: unknown
    ): err is {
      response?: {
        data?: { error?: string; message?: string };
        status?: number;
      };
      code?: string;
      message?: string;
    } => {
      return typeof err === 'object' && err !== null;
    };

    if (isAxiosError(error)) {
      const errorData = error.response?.data?.error;
      const status = error.response?.status;

      if (status === 400 && errorData && errorMap[errorData]) {
        return errorMap[errorData];
      }

      if (status === 500 || errorData === 'Failed to submit feedback') {
        return 'Failed to submit feedback. Please try again later.';
      }

      if (
        error.code === 'NETWORK_ERROR' ||
        error.message?.includes('Network Error')
      ) {
        return 'Network error. Please check your connection and try again.';
      }

      if (error.response?.data?.message) {
        return error.response.data.message;
      }
    }

    return 'Failed to submit feedback. Please try again.';
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!nurseId) {
      toast.error('Cannot submit feedback: Missing nurse ID', {
        style: { backgroundColor: '#FFFFFF', color: '#EF4444' },
        duration: 3000,
        dismissible: true,
      });
      return;
    }

    if (!validateForm()) {
      toast.error('Please fill in all required fields.', {
        style: { backgroundColor: '#FFFFFF', color: '#EF4444' },
        duration: 3000,
        dismissible: true,
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const payload = {
        feedbackId,
        recipientName,
        nurseId,
        ...formData,
      };

      const response = await axios.post(
        'https://testnurservapi.nurserv.com/nurseApi/profile/submitRating',
        payload
      );

      const feedbackSessions = JSON.parse(
        localStorage.getItem('feedbackSessions') || '{}'
      );
      feedbackSessions[feedbackId] = {
        ...feedbackSessions[feedbackId],
        feedback: formData,
        submittedAt: new Date().toISOString(),
        status: 'submitted',
        apiResponse: response.data,
      };
      localStorage.setItem(
        'feedbackSessions',
        JSON.stringify(feedbackSessions)
      );

      toast.success('Thank you for your feedback!', {
        style: { backgroundColor: '#FFFFFF', color: '#16A34A' },
        duration: 2000,
        dismissible: true,
      });

      setIsSubmitted(true);
    } catch (error) {
      console.error('Error submitting feedback:', error);
      toast.error(getErrorMessage(error));
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isSubmitted) {
    return (
      <div className='relative w-full min-h-screen flex flex-col gap-2 justify-center items-center p-4'>
        <div className='absolute inset-0 w-full h-full z-0'>
          <img
            src='../../public/Images/bg4.png'
            alt='Background Wallpaper'
            className='w-full h-full object-cover'
          />
        </div>
        <div className='absolute inset-0 w-full object-cover bg-black bg-opacity-20 z-0' />
        <Logo />
        <Card className='max-w-md w-full mx-4 z-10 bg-slate-200 '>
          <CardContent className='pt-6'>
            <div className='text-center space-y-2'>
              <h2 className='text-2xl font-semibold'>Thank You!</h2>
              <p className='text-slate-700'>
                Your feedback has been submitted successfully.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className='relative w-full min-h-screen flex flex-col gap-2 justify-center items-center p-4'>
      <div className='absolute inset-0 w-full h-full z-0'>
        <img
          src='../../public/Images/bg4.png'
          alt='Background Wallpaper'
          className='w-full h-full object-cover'
        />
      </div>

      <div className='absolute inset-0 w-full object-cover bg-black bg-opacity-20 z-0' />
      <Logo />
      <Card className='max-w-md w-full mx-3 z-10'>
        <CardHeader>
          <CardTitle>Feedback for {recipientName}</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} noValidate className='space-y-4'>
            <div className='space-y-2'>
              <Label htmlFor='name'>Your Name *</Label>
              <Input
                id='name'
                value={formData.name}
                onChange={e => handleInputChange('name', e.target.value)}
                required
                disabled={isSubmitting}
                placeholder='Your Name'
                className={
                  errors.name ? 'border-red-500 focus:border-red-500' : ''
                }
              />
              {errors.name && (
                <p className='text-red-500 text-sm'>{errors.name}</p>
              )}
            </div>

            <div className='space-y-2'>
              <Label>Rating *</Label>
              <div className='flex items-center gap-4'>
                <div className='flex space-x-2'>
                  {[1, 2, 3, 4, 5].map(star => {
                    const isHighlighted =
                      hoveredStar > 0
                        ? star <= hoveredStar
                        : star <= formData.rating;

                    return (
                      <button
                        key={star}
                        type='button'
                        onClick={() => handleInputChange('rating', star)}
                        onMouseEnter={() => setHoveredStar(star)}
                        onMouseLeave={() => setHoveredStar(0)}
                        className={`text-2xl transition-colors ${
                          isHighlighted
                            ? 'text-[#f09e22]'
                            : 'text-nursery-darkBlue'
                        } hover:text-[#f09e22]`}
                        disabled={isSubmitting}
                      >
                        <Star
                          className={`w-8 h-8 transition-colors duration-200 ${
                            isHighlighted ? 'fill-current' : ''
                          }`}
                        />
                      </button>
                    );
                  })}
                </div>
                {formData.rating > 0 && (
                  <Badge
                    variant='secondary'
                    className='text-md font-medium text-nursery-darkBlue'
                  >
                    {formData.rating} {formData.rating === 1 ? 'star' : 'stars'}
                  </Badge>
                )}
              </div>
              {errors.rating && (
                <p className='text-red-500 text-sm'>{errors.rating}</p>
              )}
            </div>

            <div className='space-y-2'>
              <Label htmlFor='comments'>Comments *</Label>
              <Textarea
                id='comments'
                value={formData.comments}
                onChange={e => handleInputChange('comments', e.target.value)}
                rows={4}
                required
                disabled={isSubmitting}
                placeholder='Your feedback matters! Share your thoughts here...'
                className={
                  errors.comments ? 'border-red-500 focus:border-red-500' : ''
                }
              />
              {errors.comments && (
                <p className='text-red-500 text-sm'>{errors.comments}</p>
              )}
            </div>

            <Button
              type='submit'
              className='w-full'
              disabled={isSubmitting || !nurseId}
            >
              {isSubmitting ? 'Submitting...' : 'Submit Feedback'}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
