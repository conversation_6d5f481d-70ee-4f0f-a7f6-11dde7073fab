import { FlatCompat } from "@eslint/eslintrc";
import js from "@eslint/js";
import path from "path";
import { fileURLToPath } from "url";
import tseslint from "typescript-eslint";


const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);


const compat = new FlatCompat({
  baseDirectory: __dirname,
  recommendedConfig: js.configs.recommended,
});

export default [
  
  {
    ignores: [
      "/node_modules/",
      "dist/",
      "build/",
      ".git/",
      "tailwind.config.ts",
      "vite.config.ts",
      "src/types/google-maps.d.ts",
    ],
  },

  
  js.configs.recommended,
  ...tseslint.configs.recommended,

  
  ...compat.config({
    extends: [
      "plugin:react/recommended",
      "plugin:react-hooks/recommended", 
      "plugin:prettier/recommended",
    ],
    plugins: ["react", "react-hooks", "unused-imports", "no-comments"],
  }),

  
  {
    files: ["**/*.ts", "**/*.tsx"],
    languageOptions: {
      parser: tseslint.parser,
      parserOptions: {
        project: "./tsconfig.app.json",
        ecmaVersion: "latest",
        sourceType: "module",
        ecmaFeatures: {
          jsx: true,
        },
      },
    },
    rules: {
      
      "unused-imports/no-unused-imports": "error",
      
      
      "unused-imports/no-unused-vars": [
        "warn",
        {
          vars: "all",
          varsIgnorePattern: "^_",
          args: "after-used", 
          argsIgnorePattern: "^_",
        },
      ],

      
      "no-comments/disallowComments": "error",
      
      
      "no-unused-vars": "off",
      "@typescript-eslint/no-unused-vars": "off",
      "react/react-in-jsx-scope": "off",
      "react/prop-types": "off",
      "react/no-unknown-property": "off",

      
      "@typescript-eslint/explicit-function-return-type": "off",
      "@typescript-eslint/explicit-module-boundary-types": "off",
      "@typescript-eslint/no-explicit-any": "warn",
    },
    settings: {
      react: {
        version: "detect",
      },
    },
  },

  
  {
    files: ["**/*.js", "**/*.jsx"],
    rules: {
      
      "unused-imports/no-unused-imports": "error",
      "react/react-in-jsx-scope": "off",
      "react/prop-types": "off",
      "react/no-unknown-property": "off",
      
      
      "no-comments/disallowComments": "error",
    },
    settings: {
      react: {
        version: "detect",
      },
    },
  },
];
