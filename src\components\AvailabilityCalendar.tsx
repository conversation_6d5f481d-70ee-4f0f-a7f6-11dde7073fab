import React from 'react';
import {
  ChevronLeft,
  ChevronRight,
  IndianRupee,
  AlertTriangle,
  Lock,
  CheckCircle,
  Calendar,
} from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';

interface AvailabilityCalendarProps {
  monthNames: string[];
  currentMonth: number;
  currentYear: number;
  goToPreviousMonth: () => void;
  goToNextMonth: () => void;
  dayNames: string[];
  renderCalendarDays: () => React.ReactNode;
  fare: string;
  handleFareChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  isFocused: boolean;
  setIsFocused: (v: boolean) => void;
  _isCreating: boolean;
  shouldDisableFareInput: boolean;
  hasExistingAvailability: boolean;
}

const AvailabilityCalendar: React.FC<AvailabilityCalendarProps> = ({
  monthNames,
  currentMonth,
  currentYear,
  goToPreviousMonth,
  goToNextMonth,
  dayNames,
  renderCalendarDays,
  fare,
  handleFareChange,
  isFocused,
  setIsFocused,
  _isCreating,
  shouldDisableFareInput,
  hasExistingAvailability,
}) => (
  <section className='p-4'>
    {}
    <div className='bg-[#F2F2F2] rounded-lg border p-4 shadow-lg'>
      <div className='flex items-center mb-4'>
        <IndianRupee className='h-6 w-5 mr-2 text-nursery-darkBlue' />
        <h3 className='text-lg font-semibold'>Hourly Rate</h3>
        {shouldDisableFareInput && (
          <Lock className='h-4 w-4 ml-2 text-gray-500' />
        )}
      </div>
      <div className='relative'>
        <div className='absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none'>
          <span className='text-gray-500 text-lg'>
            <IndianRupee className='h-4 w-4' />
          </span>
        </div>
        <Input
          type='text'
          value={fare}
          onChange={handleFareChange}
          onFocus={() => !shouldDisableFareInput && setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          placeholder='0.00'
          className={`w-full pl-8 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nursery-blue focus:border-nursery-blue text-lg ${
            shouldDisableFareInput ? 'bg-white cursor-not-allowed' : ''
          }`}
          disabled={shouldDisableFareInput}
        />
        <div className='absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none'>
          <span className='text-gray-500 text-sm'>/ Hour</span>
        </div>
      </div>

      {}
      {hasExistingAvailability && (
        <div className='mt-3 p-3 bg-amber-100 border border-amber-300 rounded-lg'>
          <div className='flex items-start'>
            <Lock className='h-4 w-4 text-amber-800 mt-0.5 mr-2 flex-shrink-0' />
            <p className='text-sm text-amber-800'>
              <strong>Rate Locked:</strong> Your hourly rate is locked at ₹
              {fare}. Contact admin to request changes.
            </p>
          </div>
        </div>
      )}

      {!hasExistingAvailability && isFocused && (
        <div className='mt-3 p-3 bg-amber-100 border border-amber-300 rounded-lg'>
          <div className='flex items-start'>
            <AlertTriangle className='h-4 w-4 text-amber-800 mt-0.5 mr-2 flex-shrink-0' />
            <p className='text-sm text-amber-800'>
              <strong>Important:</strong> Please set a competitive hourly rate
              you&apos;re comfortable with, as it cannot be changed again.
            </p>
          </div>
        </div>
      )}
      {fare && (
        <div className='mt-3 p-3 bg-green-50 border border-green-200 rounded-lg'>
          <div className='flex items-center'>
            <CheckCircle className='h-4 w-4 text-green-600 mr-2 flex-shrink-0' />
            <p className='text-sm text-green-800'>
              Rate set to <strong>₹{fare}/hour</strong>
            </p>
          </div>
        </div>
      )}
    </div>
    {}
    <div className='bg-[#F2F2F2] rounded-lg border p-4 shadow-lg mt-6'>
      <div className='flex items-center justify-between mb-4'>
        <div className='flex items-center'>
          <Calendar className='h-6 w-6 mr-2 text-nursery-darkBlue' />
          <h3 className='text-lg font-semibold'>Select Dates</h3>
        </div>
        <div className='flex items-center md:pr-3 '>
          <Button
            variant='outline'
            size='sm'
            onClick={goToPreviousMonth}
            className='p-2 shadow-md'
          >
            <ChevronLeft className='h-4 w-4' />
          </Button>
          <span className='text-lg font-medium min-w-[140px] text-center'>
            {monthNames[currentMonth]} {currentYear}
          </span>
          <Button
            variant='outline'
            size='sm'
            onClick={goToNextMonth}
            className='p-2 shadow-md'
          >
            <ChevronRight className='h-4 w-4' />
          </Button>
        </div>
      </div>

      {}
      <div className='grid grid-cols-7 gap-1 mb-2'>
        {dayNames.map(day => (
          <div
            key={day}
            className='p-2 text-center text-sm font-medium text-gray-600'
          >
            {day}
          </div>
        ))}
      </div>

      {}
      <div className='grid grid-cols-7 gap-1'>{renderCalendarDays()}</div>
      <div className='mt-4 text-sm text-gray-600'>
        <div className='flex items-center gap-4 flex-wrap'>
          <div className='flex items-center gap-2'>
            <div className='w-3 h-3 bg-green-500 rounded-full'></div>
            <span>Existing slots</span>
          </div>
          <div className='flex items-center gap-2'>
            <div className='w-3 h-3 bg-blue-500 rounded-full'></div>
            <span>New selections</span>
          </div>
          <div className='flex items-center gap-2'>
            <div className='w-3 h-3 bg-gray-400 rounded-full'></div>
            <span>Past/Today</span>
          </div>
        </div>
      </div>
    </div>
  </section>
);

export default AvailabilityCalendar;
