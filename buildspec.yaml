version: 0.2

phases:
  install:
    runtime-versions:
      nodejs: 20
    commands:
      - echo "Installing dependencies..."
      - npm ci

  pre_build:
    commands:
      - echo "Pre-build phase started on `date`"
      - echo "Setting up environment variables..."
      - export AWS_DEFAULT_REGION=$AWS_DEFAULT_REGION
      - echo "Current working directory: $CODEBUILD_SRC_DIR"

  build:
    commands:
      - echo "Build started on `date`"
      - echo "Building the application..."
      - npm run build
      - echo "Build completed on `date`"
      - echo "Build artifacts:"
      - ls -la dist/

  post_build:
    commands:
      - echo "Post-build phase started on `date`"
      - echo "Syncing build artifacts to S3..."
      - aws s3 sync dist/ s3://$S3_BUCKET_NAME --delete
      - echo "Invalidating CloudFront cache..."
      - aws cloudfront create-invalidation --distribution-id $CLOUDFRONT_DISTRIBUTION_ID --paths "/*"
      - echo "Deployment completed successfully on `date`"

artifacts:
  files:
    - '**/*'
  base-directory: dist
  discard-paths: no

cache:
  paths:
    - 'node_modules/**/*'
