import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { CalendarIcon } from 'lucide-react';
import { format } from 'date-fns';

interface PersonalDetailsFormProps {
  date: Date | undefined;
  setDate: React.Dispatch<React.SetStateAction<Date | undefined>>;
  gender: string;
  setGender: React.Dispatch<React.SetStateAction<string>>;
  email: string;
  setEmail: React.Dispatch<React.SetStateAction<string>>;
  emergencyContact: string;
  setEmergencyContact: React.Dispatch<React.SetStateAction<string>>;
  handleNext: () => void;
  handleCancel: () => void;
}

const PersonalDetailsForm = ({
  date,
  setDate,
  gender,
  setGender,
  email,
  setEmail,
  emergencyContact,
  setEmergencyContact,
  handleNext,
  handleCancel,
}: PersonalDetailsFormProps) => {
  return (
    <div className='space-y-6'>
      <div>
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant='outline'
              className='w-full h-12 justify-between text-gray-500 font-normal border rounded-md'
            >
              {date ? format(date, 'PPP') : <span>Date of Birth *</span>}
              <CalendarIcon className='h-5 w-5 opacity-50' />
            </Button>
          </PopoverTrigger>
          <PopoverContent className='w-auto p-0'>
            <Calendar
              mode='single'
              selected={date}
              onSelect={setDate}
              initialFocus
            />
          </PopoverContent>
        </Popover>
      </div>

      <div>
        <Select value={gender} onValueChange={setGender}>
          <SelectTrigger className='w-full h-12 text-gray-500 font-normal'>
            <SelectValue placeholder='Gender *' />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value='male'>Male</SelectItem>
            <SelectItem value='female'>Female</SelectItem>
            <SelectItem value='other'>Other</SelectItem>
            <SelectItem value='prefer_not_to_say'>Prefer not to say</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div>
        <Input
          type='email'
          placeholder='Enter Your Email'
          value={email}
          onChange={e => setEmail(e.target.value)}
          className='w-full h-12 text-gray-500'
        />
      </div>

      <div>
        <Input
          type='tel'
          placeholder='Emergency Contact Number'
          value={emergencyContact}
          onChange={e => setEmergencyContact(e.target.value)}
          className='w-full h-12 text-gray-500'
        />
      </div>

      <div className='pt-8'>
        <Button
          className='w-full h-12 bg-[#4AB4CE] hover:bg-[#3a91a5] text-white'
          onClick={handleNext}
        >
          Save and Next
        </Button>

        <Button
          variant='outline'
          className='w-full h-12 mt-4 border-[#4AB4CE] text-[#4AB4CE] hover:bg-[#4AB4CE]/10'
          onClick={handleCancel}
        >
          Cancel
        </Button>
      </div>
    </div>
  );
};

export default PersonalDetailsForm;
