import React from 'react';
import { MoreVertical, OctagonX } from 'lucide-react';
import { useBookingActions } from '@/hooks/useBookingActions';
import { Booking } from '@/store/api/customerApiSlice';

interface BookingHeaderProps {
  booking: Booking;
  currentServiceStatus: string;
  isAccepted: boolean;
  activeMenuId: string | null;
  setActiveMenuId: (id: string | null) => void;
  menuRef: React.RefObject<HTMLDivElement>;
  onDeclineBooking: () => void;
  updatingStatus: boolean;
  isSubmittingDecline: boolean;
}

const BookingHeader: React.FC<BookingHeaderProps> = ({
  booking,
  currentServiceStatus,
  isAccepted,
  activeMenuId,
  setActiveMenuId,
  menuRef,
  onDeclineBooking,
  updatingStatus,
  isSubmittingDecline,
}) => {
  const { getStatusBadgeClasses } = useBookingActions();
  const bookingIdStr = booking.booking_id.toString();
  const isMenuActive = activeMenuId === bookingIdStr;

  const toggleMenu = (event: React.MouseEvent) => {
    event.stopPropagation();
    setActiveMenuId(activeMenuId === bookingIdStr ? null : bookingIdStr);
  };

  return (
    <>
      <div className='flex justify-between items-center mb-3'>
        <span className={getStatusBadgeClasses(booking.booking_status)}>
          {booking.booking_status}
        </span>

        {isAccepted && currentServiceStatus === 'not_started' && (
          <div ref={menuRef}>
            <button
              onClick={toggleMenu}
              className='p-1 rounded-full hover:bg-gray-200 transition-colors'
            >
              <MoreVertical className='w-5 h-5 text-gray-500' />
            </button>

            {isMenuActive && (
              <div className='absolute right-4 top-12 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[150px]'>
                <button
                  onClick={onDeclineBooking}
                  disabled={updatingStatus || isSubmittingDecline}
                  className='w-full px-4 py-2 text-left text-gray-800 hover:bg-red-50 hover:text-red-600 rounded-lg transition-colors disabled:opacity-50'
                >
                  {isSubmittingDecline ? 'Declining...' : 'Decline Booking'}
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      {(booking.booking_status === 'Declined' ||
        booking.booking_status === 'Cancelled') &&
        booking.cancellation_reason && (
          <div className='flex items-start gap-2 mb-3'>
            <OctagonX className='w-5 h-5 text-[#EB001B] mt-0.5 flex-shrink-0' />
            <div className='text-sm font-medium text-gray-900'>
              {booking.cancellation_reason}
            </div>
          </div>
        )}
    </>
  );
};

export default BookingHeader;
