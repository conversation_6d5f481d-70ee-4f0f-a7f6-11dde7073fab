import React from 'react';
import { Button } from '@/components/ui/button';
import { FilePlus2 } from 'lucide-react';

interface DocumentUploadFormProps {
  idProofFiles: File[];
  setIdProofFiles: React.Dispatch<React.SetStateAction<File[]>>;
  experienceProofFiles: File[];
  setExperienceProofFiles: React.Dispatch<React.SetStateAction<File[]>>;
  otherDocumentFiles: File[];
  setOtherDocumentFiles: React.Dispatch<React.SetStateAction<File[]>>;
  handleSubmit: () => void;
  handleBack: () => void;
}

const DocumentUploadForm = ({
  idProofFiles,
  setIdProofFiles,
  experienceProofFiles,
  setExperienceProofFiles,
  otherDocumentFiles,
  setOtherDocumentFiles,
  handleSubmit,
  handleBack,
}: DocumentUploadFormProps) => {
  const handleFileUpload = (
    event: React.ChangeEvent<HTMLInputElement>,
    setFiles: React.Dispatch<React.SetStateAction<File[]>>
  ) => {
    if (event.target.files) {
      const filesList = Array.from(event.target.files);
      setFiles(prev => [...prev, ...filesList]);
    }
  };

  return (
    <div className='space-y-6'>
      {}
      <div className='border-b pb-4'>
        <div className='flex justify-between items-center mb-2'>
          <h3 className='text-gray-700 font-medium'>ID Proof</h3>
          <label
            htmlFor='id-proof-upload'
            className='flex items-center gap-1 text-nursery-blue cursor-pointer'
          >
            <FilePlus2 className='h-5 w-5' />
            <span>Add Files</span>
          </label>
          <input
            id='id-proof-upload'
            type='file'
            className='hidden'
            multiple
            onChange={e => handleFileUpload(e, setIdProofFiles)}
          />
        </div>
        {idProofFiles.length > 0 && (
          <div className='mt-2'>
            {idProofFiles.map((file, index) => (
              <div
                key={`id-proof-${file.name}-${file.size}-${index}`}
                className='text-sm text-gray-600'
              >
                {file.name}
              </div>
            ))}
          </div>
        )}
      </div>

      {}
      <div className='border-b pb-4'>
        <div className='flex justify-between items-center mb-2'>
          <h3 className='text-gray-700 font-medium'>Experience Proof</h3>
          <label
            htmlFor='experience-proof-upload'
            className='flex items-center gap-1 text-nursery-blue cursor-pointer'
          >
            <FilePlus2 className='h-5 w-5' />
            <span>Add Files</span>
          </label>
          <input
            id='experience-proof-upload'
            type='file'
            className='hidden'
            multiple
            onChange={e => handleFileUpload(e, setExperienceProofFiles)}
          />
        </div>
        {experienceProofFiles.length > 0 && (
          <div className='mt-2'>
            {experienceProofFiles.map((file, index) => (
              <div
                key={`experience-proof-${file.name}-${file.size}-${index}`}
                className='text-sm text-gray-600'
              >
                {file.name}
              </div>
            ))}
          </div>
        )}
      </div>

      {}
      <div className='border-b pb-4'>
        <div className='flex justify-between items-center mb-2'>
          <h3 className='text-gray-700 font-medium'>Other Documents</h3>
          <label
            htmlFor='other-documents-upload'
            className='flex items-center gap-1 text-nursery-blue cursor-pointer'
          >
            <FilePlus2 className='h-5 w-5' />
            <span>Add Files</span>
          </label>
          <input
            id='other-documents-upload'
            type='file'
            className='hidden'
            multiple
            onChange={e => handleFileUpload(e, setOtherDocumentFiles)}
          />
        </div>
        {otherDocumentFiles.length > 0 && (
          <div className='mt-2'>
            {otherDocumentFiles.map((file, index) => (
              <div
                key={`other-docs-${file.name}-${file.size}-${index}`}
                className='text-sm text-gray-600'
              >
                {file.name}
              </div>
            ))}
          </div>
        )}
      </div>

      <div className='pt-8 space-y-4'>
        <Button
          className='w-full h-12 bg-[#4AB4CE] hover:bg-[#3a91a5] text-white'
          onClick={handleSubmit}
        >
          Submit
        </Button>

        <Button
          variant='outline'
          className='w-full h-12 border-[#4AB4CE] text-[#4AB4CE] hover:bg-[#4AB4CE]/10'
          onClick={handleBack}
        >
          Back
        </Button>
      </div>
    </div>
  );
};

export default DocumentUploadForm;
