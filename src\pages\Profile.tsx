import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  ArrowLeft,
  User,
  Calendar,
  FileText,
  Building2,
  Settings,
  Shield,
  ChevronRight,
  Link,
  Share2,
} from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import bg from '/Images/bg4.png';
import {
  useGetCurrentUserProfileImageQuery,
  useGetProfileDetailsQuery,
} from '@/store/api/apiSlice';
import ResponsiveLoader from '@/components/Loader';
import { toast } from '@/utils/toast';
import SessionExpiredError from '@/components/ui/SessionExpiredError';

const HOVER_STYLES =
  'hover:shadow-lg transform-gpu transition-shadow duration-200 ease-in-out';

type RTKError = {
  status: number | string;
  data?: { error?: string; message?: string };
};

const isRTKError = (err: unknown): err is RTKError => {
  return typeof err === 'object' && err !== null && 'status' in err;
};

const handleProfileError = (error: unknown) => {
  console.error('Profile details fetch error:', error);

  if (isRTKError(error)) {
    const errorMessage = error.data?.error || error.data?.message;
    const statusCode = error.status;

    switch (statusCode) {
      case 401:
        toast.error('Session expired. Please log in again.');
        break;
      case 403:
        toast.error('Access denied. Please check your permissions.');
        break;
      case 404:
        toast.error('Profile details not found. Please try refreshing.');
        break;
      case 500:
        toast.error('Server error. Please try again later.');
        break;
      default:
        toast.error(
          errorMessage || 'Failed to load profile details. Please try again.'
        );
    }
  } else {
    if (!navigator.onLine) {
      toast.error('Network error. Please check your connection.');
    } else {
      toast.error('Failed to load profile details. Please try again.');
    }
  }
};

const Profile = () => {
  const [_personalExpanded, _setPersonalExpanded] = useState(true);
  const [_professionalExpanded, _setProfessionalExpanded] = useState(false);
  const [_loading, _setLoading] = useState(true);
  const [showAvailabilityWarning, setShowAvailabilityWarning] = useState(false);
  const [showReferralWarning, setShowReferralWarning] = useState(false);
  const navigate = useNavigate();

  const { data: profile, error, isLoading } = useGetProfileDetailsQuery();
  const profileVerified = profile?.details?.profile_verified;
  const { data: currentProfileImage, refetch: _refetch } =
    useGetCurrentUserProfileImageQuery();
  const profileImage = currentProfileImage?.profile_image?.signed_url;

  useEffect(() => {
    if (error) {
      handleProfileError(error);
    }
  }, [error]);

  if (isLoading) {
    return (
      <div>
        <ResponsiveLoader />
      </div>
    );
  }

  if (error) {
    return <SessionExpiredError />;
  }

  return (
    <div className='min-h-screen flex flex-col bg-white'>
      <header className='relative w-full  overflow-hidden text-white p-3 flex flex-col'>
        <div className='absolute inset-0 w-full h-full z-0 bg-fixed'>
          <img
            src={bg}
            alt='Background Wallpaper'
            className=' object-cover w-full '
          />
        </div>
        <div className='absolute inset-0 w-full object-cover bg-black bg-opacity-20 z-0' />
        {}
        <div className='relative z-10 h-full w-full flex items-center mb-6'>
          <button onClick={() => navigate(-1)} className='mr-4'>
            <ArrowLeft className='h-6 w-6' />
          </button>
          <h1 className='text-xl font-semibold'>My Profile</h1>
        </div>

        {}
        <div className='relative flex items-center mb-3'>
          <div className='relative'>
            <Avatar
              className={`${profileImage ? 'h-28 w-28 p-1' : 'h-16 w-16 p-2'} bg-white border-1 border-white shadow-lg`}
            >
              {profileImage ? (
                <AvatarImage
                  src={profileImage}
                  alt='Profile'
                  className='object-cover rounded-full'
                />
              ) : (
                <AvatarFallback className='bg-white text-nursery-blue text-2xl rounded-full'>
                  <User className='h-10 w-10' />
                </AvatarFallback>
              )}
            </Avatar>
          </div>
          <div className='ml-4'>
            <h2 className='text-xl font-semibold'>
              {`${profile?.details?.given_name || 'Loading'} ${profile?.details?.family_name || 'Loading...'}`}{' '}
            </h2>
            <p className='text-sm'>
              {profile?.details?.phone_number || 'Loading Phone Number...'}
            </p>
            <p className='text-sm'>
              {profile?.details?.email || 'Loading Email...'}
            </p>
          </div>
        </div>
      </header>

      {}
      <main className='flex-1'>
        <div className='py-2'>
          <MenuItemWrapper>
            <MenuItem
              icon={<User className='h-5 w-5 text-gray-500' />}
              label='Profile'
              onClick={() => navigate('/profile-details')}
            />
          </MenuItemWrapper>

          <div className='relative transition-all duration-300 ease-in-out'>
            <MenuItemWrapper disabled={!profileVerified}>
              <MenuItem
                icon={<Calendar className='h-5 w-5 text-gray-500' />}
                label='Set Availability'
                onClick={() => {
                  if (profileVerified) {
                    navigate('/availability');
                  } else {
                    setShowAvailabilityWarning(true);
                    setTimeout(() => setShowAvailabilityWarning(false), 5000);
                  }
                }}
              />
            </MenuItemWrapper>
            <div
              className={`overflow-hidden transition-all duration-300 ease-in-out ${!profileVerified && showAvailabilityWarning ? 'max-h-20 opacity-100 px-2' : 'max-h-0 opacity-0'}`}
            >
              <div className='p-3 bg-amber-100 border border-amber-300 rounded-md text-amber-800 text-sm font-semibold'>
                We&apos;re reviewing your profile. You&apos;ll be notified once
                it&apos;s verified by the admin
              </div>
            </div>
          </div>

          <MenuItemWrapper>
            <MenuItem
              icon={<FileText className='h-5 w-5 text-gray-500' />}
              label='Documents'
              onClick={() => navigate('/documents')}
            />
          </MenuItemWrapper>

          <MenuItemWrapper>
            <MenuItem
              icon={<Building2 className='h-5 w-5 text-gray-500' />}
              label='Bank Accounts'
              onClick={() => navigate('/bank-accounts')}
            />
          </MenuItemWrapper>

          <MenuItemWrapper>
            <MenuItem
              icon={<Link className='h-5 w-5 text-gray-500' />}
              label='Generate Feedback Link'
              onClick={() => {
                const state = {
                  recipientName: profile?.details?.given_name,
                  nurseId: profile?.details?.user_id,
                };
                window.open(
                  `/feedback/generate?${new URLSearchParams(state).toString()}`,
                  '_blank'
                );
              }}
            />
          </MenuItemWrapper>

          <div className='relative transition-all duration-300 ease-in-out'>
            <MenuItemWrapper disabled={!profileVerified}>
              <MenuItem
                icon={<Share2 className='h-5 w-5 text-gray-500' />}
                label='Refer a Nurse'
                onClick={() => {
                  if (profileVerified) {
                    navigate('/refer-nurse');
                  } else {
                    setShowReferralWarning(true);
                    setTimeout(() => setShowReferralWarning(false), 5000);
                  }
                }}
              />
            </MenuItemWrapper>
            <div
              className={`overflow-hidden transition-all duration-300 ease-in-out ${!profileVerified && showReferralWarning ? 'max-h-20 opacity-100 px-2' : 'max-h-0 opacity-0'}`}
            >
              <div className='p-3 bg-amber-100 border border-amber-300 rounded-md text-amber-800 text-sm font-semibold'>
                We&apos;re reviewing your profile. You&apos;ll be notified once
                it&apos;s verified by the admin
              </div>
            </div>
          </div>
          <MenuItemWrapper>
            <MenuItem
              icon={<Shield className='h-5 w-5 text-gray-500' />}
              label='Privacy Policy'
              onClick={() => navigate('/privacy-policy')}
            />
          </MenuItemWrapper>

          <MenuItemWrapper>
            <MenuItem
              icon={<Settings className='h-5 w-5 text-gray-500' />}
              label='Settings'
              onClick={() => navigate('/settings')}
            />
          </MenuItemWrapper>
        </div>
      </main>
    </div>
  );
};

interface MenuItemWrapperProps {
  children: React.ReactNode;
  disabled?: boolean;
  className?: string;
}

const MenuItemWrapper: React.FC<MenuItemWrapperProps> = ({
  children,
  disabled = false,
  className = '',
}) => (
  <div
    className={`${disabled ? 'opacity-50 cursor-not-allowed' : HOVER_STYLES} ${className}`}
  >
    {children}
  </div>
);

const MenuItem = ({
  icon,
  label,
  onClick,
}: {
  icon: React.ReactNode;
  label: string;
  onClick: () => void;
}) => {
  return (
    <button
      onClick={onClick}
      className='flex items-center justify-between w-full px-4 py-4 border-b border-gray-100'
    >
      <div className='flex items-center'>
        <div className='mr-4'>{icon}</div>
        <span className='font-medium'>{label}</span>
      </div>
      <ChevronRight className='h-5 w-5 text-gray-500' />
    </button>
  );
};

export default Profile;
