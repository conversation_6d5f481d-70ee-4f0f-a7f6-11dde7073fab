import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

interface ProfessionalDetailsFormProps {
  totalExperience: string;
  setTotalExperience: React.Dispatch<React.SetStateAction<string>>;
  selectedServices: string[];
  toggleService: (service: string) => void;
  customService: string;
  setCustomService: React.Dispatch<React.SetStateAction<string>>;
  handleNext: () => void;
  handleBack: () => void;
}

const ProfessionalDetailsForm = ({
  totalExperience,
  setTotalExperience,
  selectedServices,
  toggleService,
  customService,
  setCustomService,
  handleNext,
  handleBack,
}: ProfessionalDetailsFormProps) => {
  const services = [
    'General Nursing Care',
    'Elderly Care',
    'Pediatric Care',
    'Post-Surgery Care',
    'Maternity Care',
  ];

  return (
    <div className='space-y-6'>
      <div>
        <Input
          type='text'
          placeholder='Total Experience (in Years as a Nurse)'
          value={totalExperience}
          onChange={e => setTotalExperience(e.target.value)}
          className='w-full h-12 text-gray-500'
        />
      </div>

      <div className='space-y-2'>
        <p className='text-gray-700 font-medium'>
          What type of nursing services do you offer?
        </p>
        <p className='text-gray-500 text-sm'>You can change this later</p>

        <div className='flex flex-wrap gap-2 mt-2'>
          {services.map(service => (
            <Button
              key={service}
              variant={
                selectedServices.includes(service) ? 'default' : 'outline'
              }
              className={`rounded-full ${
                selectedServices.includes(service)
                  ? 'bg-[#4AB4CE] hover:bg-[#4AB4CE]'
                  : 'bg-white text-gray-600 hover:bg-gray-100'
              }`}
              onClick={() => toggleService(service)}
            >
              {service}
            </Button>
          ))}
        </div>
      </div>

      <div className='space-y-2'>
        <p className='text-gray-700 font-medium'>
          Do you provide any additional nursing services?
        </p>
        <Input
          type='text'
          placeholder='Add Custom Service'
          value={customService}
          onChange={e => setCustomService(e.target.value)}
          className='w-full h-12 text-gray-500'
        />
      </div>

      <div className='pt-8'>
        <Button
          className='w-full h-12 bg-[#4AB4CE] hover:bg-[#3a91a5] text-white'
          onClick={handleNext}
        >
          Save and Next
        </Button>

        <Button
          variant='outline'
          className='w-full h-12 mt-4 border-[#4AB4CE] text-[#4AB4CE] hover:bg-[#4AB4CE]/10'
          onClick={handleBack}
        >
          Back
        </Button>
      </div>
    </div>
  );
};

export default ProfessionalDetailsForm;
