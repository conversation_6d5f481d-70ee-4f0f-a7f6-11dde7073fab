import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { CalendarIcon } from 'lucide-react';
import { format } from 'date-fns';

interface PersonalDetailsFormProps {
  formData: {
    date_of_birth: Date | undefined;
    gender: string;
    emergency_contact: string;
  };
  updateFormData: (data: Partial<PersonalDetailsFormProps['formData']>) => void;
  fieldErrors: Record<string, string>;
  handleNext: () => void;
  handleCancel: () => void;
}

const PersonalDetailsForm = ({
  formData,
  updateFormData,
  fieldErrors,
  handleNext,
  handleCancel,
}: PersonalDetailsFormProps) => {
  const [open, setOpen] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleNext();
  };

  return (
    <form onSubmit={handleSubmit} noValidate className='space-y-6'>
      <div>
        <label htmlFor='DateOfBirth' className='text-gray-800 text-sm'>
          Date of Birth*
        </label>
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <Button
              variant='outline'
              className='w-full h-11 justify-between font-normal border rounded-md text-gray-700'
              onClick={() => setOpen(true)}
              type='button'
            >
              {formData.date_of_birth ? (
                format(formData.date_of_birth, 'PPP')
              ) : (
                <span>Date of Birth *</span>
              )}
              <CalendarIcon className='h-5 w-5 opacity-50' />
            </Button>
          </PopoverTrigger>
          <PopoverContent className='p-0 shadow-2xl'>
            <Calendar
              mode='single'
              selected={formData.date_of_birth}
              onSelect={date => {
                if (date) {
                  updateFormData({ date_of_birth: date });
                }
                setOpen(false);
              }}
              initialFocus
            />
          </PopoverContent>
        </Popover>
        {fieldErrors?.date_of_birth && (
          <p className='text-red-500 text-sm'>{fieldErrors.date_of_birth}</p>
        )}
      </div>

      <div>
        <label htmlFor='DateOfBirth' className='text-gray-800 text-sm'>
          Gender*
        </label>
        <Select
          value={formData.gender}
          onValueChange={value => updateFormData({ gender: value })}
        >
          <SelectTrigger className='w-full h-11 text-gray-700 font-normal'>
            <SelectValue placeholder='Gender *' />
          </SelectTrigger>
          <SelectContent className='bg-gray-200 shadow-xl rounded-lg'>
            <SelectItem value='male'>Male</SelectItem>
            <SelectItem value='female'>Female</SelectItem>
            <SelectItem value='other'>Other</SelectItem>
            <SelectItem value='prefer_not_to_say'>Prefer not to say</SelectItem>
          </SelectContent>
        </Select>
        {fieldErrors?.gender && (
          <p className='text-red-500 text-sm'>{fieldErrors.gender}</p>
        )}
      </div>

      <div>
        <label htmlFor='DateOfBirth' className='text-gray-800 text-sm'>
          Emergency Contact*
        </label>
        <Input
          type='tel'
          placeholder='Emergency Contact Number'
          value={formData.emergency_contact}
          onChange={e => updateFormData({ emergency_contact: e.target.value })}
          onFocus={e =>
            !e.target.value.startsWith('+91') &&
            updateFormData({ emergency_contact: '+91' })
          }
          className='w-full h-11 text-gray-700'
        />
        {fieldErrors?.emergency_contact && (
          <p className='text-red-500 text-sm'>
            {fieldErrors.emergency_contact}
          </p>
        )}
      </div>

      <div className='pt-0'>
        <Button
          className='w-full h-11 bg-[#4AB4CE] hover:bg-[#3a91a5] text-white'
          type='submit'
        >
          Save and Next
        </Button>

        <Button
          variant='outline'
          className='w-full h-11 mt-4 border-[#4AB4CE] text-[#4AB4CE] hover:bg-[#4AB4CE]/10'
          onClick={handleCancel}
          type='button'
        >
          Cancel
        </Button>
      </div>
    </form>
  );
};

export default PersonalDetailsForm;
